{"ast": null, "code": "var _jsxFileName = \"E:\\\\test_refactor\\\\portofolio\\\\mahmoud-portfolio\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React from \"react\";\nimport Navbar from \"./components/Navbar\";\nimport Hero from \"./components/Hero\";\nimport About from \"./components/About\";\nimport Services from \"./components/Services\";\nimport Portfolio from \"./components/Portfolio\";\nimport Contact from \"./components/Contact\";\nimport Footer from \"./components/Footer\";\nimport useScrollAnimation from \"./hooks/useScrollAnimation\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  // Initialize scroll animations\n  useScrollAnimation();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App\",\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Hero, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(About, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Services, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Portfolio, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Contact, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"U4mNXwh/zxW2waG/iM5XkjSpYcw=\", false, function () {\n  return [useScrollAnimation];\n});\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON>", "Hero", "About", "Services", "Portfolio", "Contact", "Footer", "useScrollAnimation", "jsxDEV", "_jsxDEV", "App", "_s", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["E:/test_refactor/portofolio/mahmoud-portfolio/src/App.js"], "sourcesContent": ["import React from \"react\";\nimport Navbar from \"./components/Navbar\";\nimport Hero from \"./components/Hero\";\nimport About from \"./components/About\";\nimport Services from \"./components/Services\";\nimport Portfolio from \"./components/Portfolio\";\nimport Contact from \"./components/Contact\";\nimport Footer from \"./components/Footer\";\nimport useScrollAnimation from \"./hooks/useScrollAnimation\";\n\nfunction App() {\n  // Initialize scroll animations\n  useScrollAnimation();\n\n  return (\n    <div className=\"App\">\n      <Navbar />\n      <Hero />\n      <About />\n      <Services />\n      <Portfolio />\n      <Contact />\n      <Footer />\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,kBAAkB,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb;EACAJ,kBAAkB,CAAC,CAAC;EAEpB,oBACEE,OAAA;IAAKG,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClBJ,OAAA,CAACT,MAAM;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACVR,OAAA,CAACR,IAAI;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACRR,OAAA,CAACP,KAAK;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACTR,OAAA,CAACN,QAAQ;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACZR,OAAA,CAACL,SAAS;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACbR,OAAA,CAACJ,OAAO;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACXR,OAAA,CAACH,MAAM;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV;AAACN,EAAA,CAfQD,GAAG;EAAA,QAEVH,kBAAkB;AAAA;AAAAW,EAAA,GAFXR,GAAG;AAiBZ,eAAeA,GAAG;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}