import React from 'react';
import { FaEnvelope, FaPhone, FaMapMarkerAlt, FaGithub, FaLinkedin } from 'react-icons/fa';

const CV = () => {
  const handlePrint = () => {
    window.print();
  };

  return (
    <div className="max-w-4xl mx-auto bg-white shadow-lg print:shadow-none">
      {/* Print Button - Hidden in print */}
      <div className="p-4 print:hidden">
        <button 
          onClick={handlePrint}
          className="btn btn-primary"
        >
          Print / Save as PDF
        </button>
      </div>

      {/* CV Content */}
      <div className="p-8 print:p-6">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-2"><PERSON></h1>
          <h2 className="text-xl text-gray-600 mb-4">Backend Developer</h2>
          
          {/* Contact Info */}
          <div className="flex flex-wrap justify-center gap-4 text-sm text-gray-600">
            <div className="flex items-center gap-1">
              <FaEnvelope size={14} />
              <span><EMAIL></span>
            </div>
            <div className="flex items-center gap-1">
              <FaPhone size={14} />
              <span>+01018551242</span>
            </div>
            <div className="flex items-center gap-1">
              <FaMapMarkerAlt size={14} />
              <span>Assiut, Egypt</span>
            </div>
            <div className="flex items-center gap-1">
              <FaGithub size={14} />
              <span>github.com/mohamedayman</span>
            </div>
            <div className="flex items-center gap-1">
              <FaLinkedin size={14} />
              <span>linkedin.com/in/mohamedayman</span>
            </div>
          </div>
        </div>

        {/* Professional Summary */}
        <section className="mb-6">
          <h3 className="text-lg font-bold text-gray-800 border-b-2 border-primary-600 pb-1 mb-3">
            Professional Summary
          </h3>
          <p className="text-gray-700 leading-relaxed">
            Passionate Backend Developer with expertise in creating full-stack web applications. 
            Specialized in building robust e-commerce platforms and have successfully developed 
            WebDecor&More, a comprehensive home decor platform handling 100+ product listings with 
            optimized performance. Improved system response time by 40% using optimized backend 
            queries in Node.js, demonstrating commitment to efficient and scalable solutions.
          </p>
        </section>

        {/* Technical Skills */}
        <section className="mb-6">
          <h3 className="text-lg font-bold text-gray-800 border-b-2 border-primary-600 pb-1 mb-3">
            Technical Skills
          </h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h4 className="font-semibold text-gray-700 mb-2">Backend Technologies</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Node.js & Express.js</li>
                <li>• MongoDB & Database Design</li>
                <li>• RESTful APIs Development</li>
                <li>• Authentication & Authorization</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-gray-700 mb-2">Frontend Technologies</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• HTML5, CSS3, JavaScript</li>
                <li>• React.js & Modern Frameworks</li>
                <li>• Responsive Web Design</li>
                <li>• Tailwind CSS</li>
              </ul>
            </div>
          </div>
        </section>

        {/* Projects */}
        <section className="mb-6">
          <h3 className="text-lg font-bold text-gray-800 border-b-2 border-primary-600 pb-1 mb-3">
            Key Projects
          </h3>
          
          <div className="mb-4">
            <h4 className="font-semibold text-gray-800">WebDecor&More Project</h4>
            <p className="text-sm text-gray-600 mb-2">Full-Stack E-commerce Platform | 2024</p>
            <ul className="text-sm text-gray-700 space-y-1 ml-4">
              <li>• Developed a full-stack web application for an e-commerce platform focused on home decor</li>
              <li>• Utilized technologies including Node.js, MongoDB, Express.js, HTML, CSS, and JavaScript</li>
              <li>• Implemented features such as user authentication, product catalog, and shopping cart functionality</li>
              <li>• Developed a full-stack decor web app that handled product listings for 100+ items</li>
              <li>• Improved system response time by 40% using optimized backend queries in Node.js</li>
            </ul>
          </div>
        </section>

        {/* Experience */}
        <section className="mb-6">
          <h3 className="text-lg font-bold text-gray-800 border-b-2 border-primary-600 pb-1 mb-3">
            Experience
          </h3>
          
          <div className="mb-4">
            <h4 className="font-semibold text-gray-800">Backend Developer</h4>
            <p className="text-sm text-gray-600 mb-2">Freelance | 2021 - Present</p>
            <ul className="text-sm text-gray-700 space-y-1 ml-4">
              <li>• Developed and maintained backend systems for various web applications</li>
              <li>• Implemented secure authentication and authorization systems</li>
              <li>• Optimized database queries and improved application performance</li>
              <li>• Collaborated with frontend developers to create seamless user experiences</li>
            </ul>
          </div>
        </section>

        {/* Education */}
        <section className="mb-6">
          <h3 className="text-lg font-bold text-gray-800 border-b-2 border-primary-600 pb-1 mb-3">
            Education
          </h3>
          
          <div>
            <h4 className="font-semibold text-gray-800">Computer Science</h4>
            <p className="text-sm text-gray-600">University Name | Assiut, Egypt</p>
          </div>
        </section>

        {/* Languages */}
        <section className="mb-6">
          <h3 className="text-lg font-bold text-gray-800 border-b-2 border-primary-600 pb-1 mb-3">
            Languages
          </h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <span className="font-semibold text-gray-700">Arabic:</span>
              <span className="text-gray-600 ml-2">Native</span>
            </div>
            <div>
              <span className="font-semibold text-gray-700">English:</span>
              <span className="text-gray-600 ml-2">Professional</span>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};

export default CV;
