{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useEffect } from 'react';\nconst useScrollAnimation = () => {\n  _s();\n  useEffect(() => {\n    const observerOptions = {\n      threshold: 0.1,\n      rootMargin: '0px 0px -50px 0px'\n    };\n    const observer = new IntersectionObserver(entries => {\n      entries.forEach(entry => {\n        if (entry.isIntersecting) {\n          entry.target.classList.add('visible');\n        }\n      });\n    }, observerOptions);\n\n    // Observe all elements with animate-on-scroll class\n    const animatedElements = document.querySelectorAll('.animate-on-scroll');\n    animatedElements.forEach(el => {\n      observer.observe(el);\n    });\n\n    // Cleanup\n    return () => {\n      animatedElements.forEach(el => {\n        observer.unobserve(el);\n      });\n    };\n  }, []);\n};\n_s(useScrollAnimation, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\nexport default useScrollAnimation;", "map": {"version": 3, "names": ["useEffect", "useScrollAnimation", "_s", "observerOptions", "threshold", "rootMargin", "observer", "IntersectionObserver", "entries", "for<PERSON>ach", "entry", "isIntersecting", "target", "classList", "add", "animatedElements", "document", "querySelectorAll", "el", "observe", "unobserve"], "sources": ["E:/test_refactor/portofolio/mahmoud-portfolio/src/hooks/useScrollAnimation.js"], "sourcesContent": ["import { useEffect } from 'react';\n\nconst useScrollAnimation = () => {\n  useEffect(() => {\n    const observerOptions = {\n      threshold: 0.1,\n      rootMargin: '0px 0px -50px 0px'\n    };\n\n    const observer = new IntersectionObserver((entries) => {\n      entries.forEach(entry => {\n        if (entry.isIntersecting) {\n          entry.target.classList.add('visible');\n        }\n      });\n    }, observerOptions);\n\n    // Observe all elements with animate-on-scroll class\n    const animatedElements = document.querySelectorAll('.animate-on-scroll');\n    animatedElements.forEach(el => {\n      observer.observe(el);\n    });\n\n    // Cleanup\n    return () => {\n      animatedElements.forEach(el => {\n        observer.unobserve(el);\n      });\n    };\n  }, []);\n};\n\nexport default useScrollAnimation;\n"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,OAAO;AAEjC,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/BF,SAAS,CAAC,MAAM;IACd,MAAMG,eAAe,GAAG;MACtBC,SAAS,EAAE,GAAG;MACdC,UAAU,EAAE;IACd,CAAC;IAED,MAAMC,QAAQ,GAAG,IAAIC,oBAAoB,CAAEC,OAAO,IAAK;MACrDA,OAAO,CAACC,OAAO,CAACC,KAAK,IAAI;QACvB,IAAIA,KAAK,CAACC,cAAc,EAAE;UACxBD,KAAK,CAACE,MAAM,CAACC,SAAS,CAACC,GAAG,CAAC,SAAS,CAAC;QACvC;MACF,CAAC,CAAC;IACJ,CAAC,EAAEX,eAAe,CAAC;;IAEnB;IACA,MAAMY,gBAAgB,GAAGC,QAAQ,CAACC,gBAAgB,CAAC,oBAAoB,CAAC;IACxEF,gBAAgB,CAACN,OAAO,CAACS,EAAE,IAAI;MAC7BZ,QAAQ,CAACa,OAAO,CAACD,EAAE,CAAC;IACtB,CAAC,CAAC;;IAEF;IACA,OAAO,MAAM;MACXH,gBAAgB,CAACN,OAAO,CAACS,EAAE,IAAI;QAC7BZ,QAAQ,CAACc,SAAS,CAACF,EAAE,CAAC;MACxB,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;AACR,CAAC;AAAChB,EAAA,CA5BID,kBAAkB;AA8BxB,eAAeA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}