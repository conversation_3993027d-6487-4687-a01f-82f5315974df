{"ast": null, "code": "var _jsxFileName = \"E:\\\\test_refactor\\\\portofolio\\\\mahmoud-portfolio\\\\src\\\\components\\\\About.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from \"react\";\nimport { FaDownload } from \"react-icons/fa\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst About = () => {\n  _s();\n  const [skillsAnimated, setSkillsAnimated] = useState(false);\n  const skillsRef = useRef(null);\n  const skills = [{\n    name: \"HTML/CSS\",\n    percentage: 95\n  }, {\n    name: \"JavaScript\",\n    percentage: 90\n  }, {\n    name: \"React\",\n    percentage: 85\n  }, {\n    name: \"Node.js\",\n    percentage: 90\n  }, {\n    name: \"MongoDB\",\n    percentage: 85\n  }, {\n    name: \"Express.js\",\n    percentage: 90\n  }];\n  const personalInfo = [{\n    label: \"Name\",\n    value: \"<PERSON>\"\n  }, {\n    label: \"Email\",\n    value: \"<EMAIL>\"\n  }, {\n    label: \"Phone\",\n    value: \"+01018551242\"\n  }, {\n    label: \"Location\",\n    value: \"Assiut, Egypt\"\n  }, {\n    label: \"Experience\",\n    value: \"2+ Years\"\n  }, {\n    label: \"Freelance\",\n    value: \"Available\"\n  }];\n  useEffect(() => {\n    const observer = new IntersectionObserver(entries => {\n      entries.forEach(entry => {\n        if (entry.isIntersecting && !skillsAnimated) {\n          setSkillsAnimated(true);\n        }\n      });\n    }, {\n      threshold: 0.3\n    });\n    if (skillsRef.current) {\n      observer.observe(skillsRef.current);\n    }\n    return () => {\n      if (skillsRef.current) {\n        observer.unobserve(skillsRef.current);\n      }\n    };\n  }, [skillsAnimated]);\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"about\",\n    className: \"section-padding bg-gray-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container-custom\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title\",\n          children: \"About Me\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"section-subtitle\",\n          children: \"Get to know more about me\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-start\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-on-scroll\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-2xl md:text-3xl font-bold mb-6 text-gray-800\",\n            children: \"I'm a Backend Developer\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mb-8 leading-relaxed\",\n            children: \"I'm a passionate backend developer with expertise in creating full-stack web applications. I specialize in building robust e-commerce platforms and have successfully developed WebDecor&More, a comprehensive home decor platform handling 100+ product listings with optimized performance. I improved system response time by 40% using optimized backend queries in Node.js, demonstrating my commitment to efficient and scalable solutions.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-8\",\n            children: personalInfo.map((info, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-center py-2 border-b border-gray-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-semibold text-gray-700\",\n                children: [info.label, \":\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-600\",\n                children: info.value\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col sm:flex-row gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                // Simple alert with CV info as fallback\n                const cvInfo = `\nMohamed Ayman - Backend Developer\n\n📧 <EMAIL>\n📱 +01018551242\n📍 Assiut, Egypt\n\nProfessional Summary:\nPassionate Backend Developer with expertise in creating full-stack web applications. Specialized in building robust e-commerce platforms and have successfully developed WebDecor&More, a comprehensive home decor platform handling 100+ product listings with optimized performance. Improved system response time by 40% using optimized backend queries in Node.js.\n\nKey Project - WebDecor&More:\n• Full-stack e-commerce platform for home decor\n• Technologies: Node.js, MongoDB, Express.js, HTML, CSS, JavaScript\n• 100+ product listings with optimized performance\n• 40% improvement in system response time\n\nTechnical Skills:\nBackend: Node.js, Express.js, MongoDB, RESTful APIs\nFrontend: HTML5, CSS3, JavaScript, React.js, Tailwind CSS\n\nExperience:\nBackend Developer (Freelance) | 2021 - Present\n• Developed and maintained backend systems\n• Implemented secure authentication systems\n• Optimized database queries and performance\n• Collaborated with frontend developers\n                  `;\n\n                // Try to copy to clipboard\n                if (navigator.clipboard) {\n                  navigator.clipboard.writeText(cvInfo).then(() => {\n                    alert(\"CV information copied to clipboard!\");\n                  }).catch(() => {\n                    alert(cvInfo);\n                  });\n                } else {\n                  alert(cvInfo);\n                }\n              },\n              className: \"btn btn-outline inline-flex items-center gap-2 justify-center border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white\",\n              children: \"\\uD83D\\uDCCB Copy CV Info\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/Mohamed_Ayman_CV_2025.pdf\",\n              download: \"Mohamed_Ayman_CV_2025.pdf\",\n              className: \"btn btn-primary inline-flex items-center gap-2 justify-center\",\n              children: [/*#__PURE__*/_jsxDEV(FaDownload, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 17\n              }, this), \"Download CV (PDF)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                try {\n                  // Create a simple CV page\n                  const newWindow = window.open(\"\", \"_blank\", \"width=800,height=1000,scrollbars=yes,resizable=yes\");\n                  if (!newWindow) {\n                    alert(\"Please allow popups for this site to view the CV\");\n                    return;\n                  }\n                  newWindow.document.write(`\n                    <!DOCTYPE html>\n                    <html>\n                    <head>\n                      <title>Mohamed Ayman - CV</title>\n                      <meta charset=\"UTF-8\">\n                      <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n                      <script src=\"https://cdn.tailwindcss.com\"></meta>\n                      <link href=\"https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap\" rel=\"stylesheet\">\n                      <style>\n                        body {\n                          font-family: 'Poppins', sans-serif;\n                          background-color: #f8fafc;\n                        }\n                        .cv-container {\n                          max-width: 210mm;\n                          margin: 20px auto;\n                          background: white;\n                          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n                          border-radius: 8px;\n                          overflow: hidden;\n                        }\n                        @media print {\n                          @page {\n                            margin: 0.5in;\n                            size: A4;\n                          }\n                          body {\n                            font-size: 12pt;\n                            line-height: 1.4;\n                            background: white !important;\n                          }\n                          .cv-container {\n                            box-shadow: none !important;\n                            margin: 0 !important;\n                            border-radius: 0 !important;\n                          }\n                          .print-hidden {\n                            display: none !important;\n                          }\n                          .page-break {\n                            page-break-before: always;\n                          }\n                        }\n                        .section-border {\n                          border-bottom: 2px solid #3b82f6;\n                          padding-bottom: 4px;\n                          margin-bottom: 12px;\n                        }\n                        .skill-item {\n                          background: #f1f5f9;\n                          padding: 4px 8px;\n                          border-radius: 4px;\n                          font-size: 0.875rem;\n                        }\n                      </style>\n                    </head>\n                    <body>\n                      <div class=\"cv-container\">\n                        <div class=\"print-hidden p-4 bg-gray-100 border-b\">\n                          <div class=\"flex justify-between items-center\">\n                            <h3 class=\"text-lg font-semibold text-gray-800\">Mohamed Ayman - CV</h3>\n                            <button onclick=\"window.print()\" class=\"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\">\n                              🖨️ Print / Save as PDF\n                            </button>\n                          </div>\n                        </div>\n\n                        <div class=\"p-8\">\n                          <!-- Header -->\n                          <div class=\"text-center mb-8 pb-6 border-b-2 border-blue-600\">\n                            <h1 class=\"text-4xl font-bold text-gray-800 mb-2\">Mohamed Ayman</h1>\n                            <h2 class=\"text-xl text-blue-600 font-semibold mb-4\">Backend Developer</h2>\n                            <div class=\"flex flex-wrap justify-center gap-6 text-sm text-gray-600\">\n                              <div class=\"flex items-center gap-2\">\n                                <span class=\"text-blue-600\">📧</span>\n                                <span><EMAIL></span>\n                              </div>\n                              <div class=\"flex items-center gap-2\">\n                                <span class=\"text-blue-600\">📱</span>\n                                <span>+01018551242</span>\n                              </div>\n                              <div class=\"flex items-center gap-2\">\n                                <span class=\"text-blue-600\">📍</span>\n                                <span>Assiut, Egypt</span>\n                              </div>\n                            </div>\n                          </div>\n\n                          <!-- Professional Summary -->\n                          <section class=\"mb-8\">\n                            <h3 class=\"section-border text-xl font-bold text-gray-800\">Professional Summary</h3>\n                            <div class=\"bg-gray-50 p-4 rounded-lg\">\n                              <p class=\"text-gray-700 leading-relaxed\">\n                                Passionate Backend Developer with expertise in creating full-stack web applications.\n                                Specialized in building robust e-commerce platforms and have successfully developed\n                                <strong>WebDecor&More</strong>, a comprehensive home decor platform handling 100+ product listings with\n                                optimized performance. Improved system response time by <strong>40%</strong> using optimized backend\n                                queries in Node.js.\n                              </p>\n                            </div>\n                          </section>\n\n                          <!-- Technical Skills -->\n                          <section class=\"mb-8\">\n                            <h3 class=\"section-border text-xl font-bold text-gray-800\">Technical Skills</h3>\n                            <div class=\"grid grid-cols-2 gap-6\">\n                              <div class=\"bg-blue-50 p-4 rounded-lg\">\n                                <h4 class=\"font-bold text-blue-800 mb-3 text-center\">Backend Technologies</h4>\n                                <div class=\"space-y-2\">\n                                  <div class=\"skill-item text-center\">Node.js & Express.js</div>\n                                  <div class=\"skill-item text-center\">MongoDB & Database Design</div>\n                                  <div class=\"skill-item text-center\">RESTful APIs Development</div>\n                                  <div class=\"skill-item text-center\">Authentication & Authorization</div>\n                                </div>\n                              </div>\n                              <div class=\"bg-green-50 p-4 rounded-lg\">\n                                <h4 class=\"font-bold text-green-800 mb-3 text-center\">Frontend Technologies</h4>\n                                <div class=\"space-y-2\">\n                                  <div class=\"skill-item text-center\">HTML5, CSS3, JavaScript</div>\n                                  <div class=\"skill-item text-center\">React.js & Modern Frameworks</div>\n                                  <div class=\"skill-item text-center\">Responsive Web Design</div>\n                                  <div class=\"skill-item text-center\">Tailwind CSS</div>\n                                </div>\n                              </div>\n                            </div>\n                          </section>\n\n                          <!-- Key Projects -->\n                          <section class=\"mb-8\">\n                            <h3 class=\"section-border text-xl font-bold text-gray-800\">Key Projects</h3>\n                            <div class=\"bg-gradient-to-r from-purple-50 to-pink-50 p-6 rounded-lg border-l-4 border-purple-500\">\n                              <div class=\"flex justify-between items-start mb-3\">\n                                <h4 class=\"text-lg font-bold text-purple-800\">WebDecor&More Project</h4>\n                                <span class=\"bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-semibold\">2024</span>\n                              </div>\n                              <p class=\"text-purple-700 font-medium mb-3\">Full-Stack E-commerce Platform</p>\n                              <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                                <div>\n                                  <h5 class=\"font-semibold text-gray-800 mb-2\">🛠️ Technologies Used:</h5>\n                                  <div class=\"flex flex-wrap gap-2\">\n                                    <span class=\"bg-green-100 text-green-800 px-2 py-1 rounded text-xs\">Node.js</span>\n                                    <span class=\"bg-green-100 text-green-800 px-2 py-1 rounded text-xs\">MongoDB</span>\n                                    <span class=\"bg-green-100 text-green-800 px-2 py-1 rounded text-xs\">Express.js</span>\n                                    <span class=\"bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs\">JavaScript</span>\n                                    <span class=\"bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs\">HTML/CSS</span>\n                                  </div>\n                                </div>\n                                <div>\n                                  <h5 class=\"font-semibold text-gray-800 mb-2\">🎯 Key Achievements:</h5>\n                                  <ul class=\"text-sm text-gray-700 space-y-1\">\n                                    <li>✅ 100+ product listings</li>\n                                    <li>✅ 40% performance improvement</li>\n                                    <li>✅ Full authentication system</li>\n                                    <li>✅ Shopping cart functionality</li>\n                                  </ul>\n                                </div>\n                              </div>\n                            </div>\n                          </section>\n\n                          <!-- Experience -->\n                          <section class=\"mb-8\">\n                            <h3 class=\"section-border text-xl font-bold text-gray-800\">Professional Experience</h3>\n                            <div class=\"bg-gray-50 p-6 rounded-lg border-l-4 border-blue-500\">\n                              <div class=\"flex justify-between items-start mb-3\">\n                                <h4 class=\"text-lg font-bold text-blue-800\">Backend Developer</h4>\n                                <span class=\"bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-semibold\">2021 - Present</span>\n                              </div>\n                              <p class=\"text-blue-700 font-medium mb-4\">Freelance</p>\n                              <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                                <ul class=\"text-sm text-gray-700 space-y-2\">\n                                  <li class=\"flex items-start gap-2\">\n                                    <span class=\"text-green-500 mt-1\">▶</span>\n                                    <span>Developed and maintained backend systems for web applications</span>\n                                  </li>\n                                  <li class=\"flex items-start gap-2\">\n                                    <span class=\"text-green-500 mt-1\">▶</span>\n                                    <span>Implemented secure authentication and authorization systems</span>\n                                  </li>\n                                </ul>\n                                <ul class=\"text-sm text-gray-700 space-y-2\">\n                                  <li class=\"flex items-start gap-2\">\n                                    <span class=\"text-green-500 mt-1\">▶</span>\n                                    <span>Optimized database queries and improved application performance</span>\n                                  </li>\n                                  <li class=\"flex items-start gap-2\">\n                                    <span class=\"text-green-500 mt-1\">▶</span>\n                                    <span>Collaborated with frontend developers for seamless user experiences</span>\n                                  </li>\n                                </ul>\n                              </div>\n                            </div>\n                          </section>\n\n                          <!-- Languages & Education -->\n                          <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8\">\n                            <section>\n                              <h3 class=\"section-border text-xl font-bold text-gray-800\">Languages</h3>\n                              <div class=\"space-y-3\">\n                                <div class=\"bg-green-50 p-3 rounded-lg flex justify-between items-center\">\n                                  <span class=\"font-semibold text-green-800\">Arabic</span>\n                                  <span class=\"bg-green-200 text-green-800 px-3 py-1 rounded-full text-sm font-semibold\">Native</span>\n                                </div>\n                                <div class=\"bg-blue-50 p-3 rounded-lg flex justify-between items-center\">\n                                  <span class=\"font-semibold text-blue-800\">English</span>\n                                  <span class=\"bg-blue-200 text-blue-800 px-3 py-1 rounded-full text-sm font-semibold\">Professional</span>\n                                </div>\n                              </div>\n                            </section>\n\n                            <section>\n                              <h3 class=\"section-border text-xl font-bold text-gray-800\">Education</h3>\n                              <div class=\"bg-yellow-50 p-4 rounded-lg border-l-4 border-yellow-500\">\n                                <h4 class=\"font-bold text-yellow-800 mb-2\">Computer Science</h4>\n                                <p class=\"text-yellow-700 text-sm\">University • Assiut, Egypt</p>\n                              </div>\n                            </section>\n                          </div>\n\n                          <!-- Footer -->\n                          <div class=\"text-center pt-6 border-t-2 border-gray-200\">\n                            <p class=\"text-gray-600 text-sm\">\n                              Thank you for considering my application. I look forward to discussing how I can contribute to your team.\n                            </p>\n                            <div class=\"mt-4 flex justify-center space-x-4 text-sm text-gray-500\">\n                              <span>📧 <EMAIL></span>\n                              <span>📱 +01018551242</span>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    </body>\n                    </html>\n                  `);\n                  newWindow.document.close();\n                } catch (error) {\n                  console.error(\"Error opening CV:\", error);\n                  alert(\"Error opening CV. Please try again.\");\n                }\n              },\n              className: \"btn btn-secondary inline-flex items-center gap-2 justify-center\",\n              children: \"\\uD83D\\uDC41\\uFE0F View CV\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-on-scroll\",\n          ref: skillsRef,\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-2xl md:text-3xl font-bold mb-8 text-gray-800\",\n            children: \"My Skills\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: skills.map((skill, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"skill-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between items-center mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium text-gray-700\",\n                  children: skill.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 421,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-500\",\n                  children: [skill.percentage, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 424,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"skill-bar\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `skill-progress transition-all duration-1000 ease-out ${skillsAnimated ? \"w-full\" : \"w-0\"}`,\n                  style: {\n                    width: skillsAnimated ? `${skill.percentage}%` : \"0%\",\n                    transitionDelay: `${index * 200}ms`\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 429,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 md:grid-cols-3 gap-6 mt-12\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl font-bold text-primary-600 mb-2\",\n                children: \"50+\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 446,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-gray-600\",\n                children: \"Projects Completed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 449,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl font-bold text-primary-600 mb-2\",\n                children: \"30+\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 452,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-gray-600\",\n                children: \"Happy Clients\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 455,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 451,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl font-bold text-primary-600 mb-2\",\n                children: \"3+\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 458,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-gray-600\",\n                children: \"Years Experience\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 461,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 457,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 50,\n    columnNumber: 5\n  }, this);\n};\n_s(About, \"NsnoEHHXX8aKTi9c/SBK3vjqxVM=\");\n_c = About;\nexport default About;\nvar _c;\n$RefreshReg$(_c, \"About\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "FaDownload", "jsxDEV", "_jsxDEV", "About", "_s", "skillsAnimated", "setSkillsAnimated", "skillsRef", "skills", "name", "percentage", "personalInfo", "label", "value", "observer", "IntersectionObserver", "entries", "for<PERSON>ach", "entry", "isIntersecting", "threshold", "current", "observe", "unobserve", "id", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "info", "index", "onClick", "cvInfo", "navigator", "clipboard", "writeText", "then", "alert", "catch", "href", "download", "newWindow", "window", "open", "document", "write", "close", "error", "console", "ref", "skill", "style", "width", "transitionDelay", "_c", "$RefreshReg$"], "sources": ["E:/test_refactor/portofolio/mahmoud-portfolio/src/components/About.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from \"react\";\nimport { FaDownload } from \"react-icons/fa\";\n\nconst About = () => {\n  const [skillsAnimated, setSkillsAnimated] = useState(false);\n  const skillsRef = useRef(null);\n\n  const skills = [\n    { name: \"HTML/CSS\", percentage: 95 },\n    { name: \"JavaScript\", percentage: 90 },\n    { name: \"React\", percentage: 85 },\n    { name: \"Node.js\", percentage: 90 },\n    { name: \"MongoDB\", percentage: 85 },\n    { name: \"Express.js\", percentage: 90 },\n  ];\n\n  const personalInfo = [\n    { label: \"Name\", value: \"<PERSON>\" },\n    { label: \"Email\", value: \"<EMAIL>\" },\n    { label: \"Phone\", value: \"+01018551242\" },\n    { label: \"Location\", value: \"Assiut, Egypt\" },\n    { label: \"Experience\", value: \"2+ Years\" },\n    { label: \"Freelance\", value: \"Available\" },\n  ];\n\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      (entries) => {\n        entries.forEach((entry) => {\n          if (entry.isIntersecting && !skillsAnimated) {\n            setSkillsAnimated(true);\n          }\n        });\n      },\n      { threshold: 0.3 }\n    );\n\n    if (skillsRef.current) {\n      observer.observe(skillsRef.current);\n    }\n\n    return () => {\n      if (skillsRef.current) {\n        observer.unobserve(skillsRef.current);\n      }\n    };\n  }, [skillsAnimated]);\n\n  return (\n    <section id=\"about\" className=\"section-padding bg-gray-50\">\n      <div className=\"container-custom\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"section-title\">About Me</h2>\n          <p className=\"section-subtitle\">Get to know more about me</p>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-start\">\n          {/* About Text & Info */}\n          <div className=\"animate-on-scroll\">\n            <h3 className=\"text-2xl md:text-3xl font-bold mb-6 text-gray-800\">\n              I'm a Backend Developer\n            </h3>\n            <p className=\"text-gray-600 mb-8 leading-relaxed\">\n              I'm a passionate backend developer with expertise in creating\n              full-stack web applications. I specialize in building robust\n              e-commerce platforms and have successfully developed\n              WebDecor&More, a comprehensive home decor platform handling 100+\n              product listings with optimized performance. I improved system\n              response time by 40% using optimized backend queries in Node.js,\n              demonstrating my commitment to efficient and scalable solutions.\n            </p>\n\n            {/* Personal Info Grid */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-8\">\n              {personalInfo.map((info, index) => (\n                <div\n                  key={index}\n                  className=\"flex justify-between items-center py-2 border-b border-gray-200\"\n                >\n                  <span className=\"font-semibold text-gray-700\">\n                    {info.label}:\n                  </span>\n                  <span className=\"text-gray-600\">{info.value}</span>\n                </div>\n              ))}\n            </div>\n\n            {/* Download CV Buttons */}\n            <div className=\"flex flex-col sm:flex-row gap-4\">\n              {/* Simple CV View Button */}\n              <button\n                onClick={() => {\n                  // Simple alert with CV info as fallback\n                  const cvInfo = `\nMohamed Ayman - Backend Developer\n\n📧 <EMAIL>\n📱 +01018551242\n📍 Assiut, Egypt\n\nProfessional Summary:\nPassionate Backend Developer with expertise in creating full-stack web applications. Specialized in building robust e-commerce platforms and have successfully developed WebDecor&More, a comprehensive home decor platform handling 100+ product listings with optimized performance. Improved system response time by 40% using optimized backend queries in Node.js.\n\nKey Project - WebDecor&More:\n• Full-stack e-commerce platform for home decor\n• Technologies: Node.js, MongoDB, Express.js, HTML, CSS, JavaScript\n• 100+ product listings with optimized performance\n• 40% improvement in system response time\n\nTechnical Skills:\nBackend: Node.js, Express.js, MongoDB, RESTful APIs\nFrontend: HTML5, CSS3, JavaScript, React.js, Tailwind CSS\n\nExperience:\nBackend Developer (Freelance) | 2021 - Present\n• Developed and maintained backend systems\n• Implemented secure authentication systems\n• Optimized database queries and performance\n• Collaborated with frontend developers\n                  `;\n\n                  // Try to copy to clipboard\n                  if (navigator.clipboard) {\n                    navigator.clipboard\n                      .writeText(cvInfo)\n                      .then(() => {\n                        alert(\"CV information copied to clipboard!\");\n                      })\n                      .catch(() => {\n                        alert(cvInfo);\n                      });\n                  } else {\n                    alert(cvInfo);\n                  }\n                }}\n                className=\"btn btn-outline inline-flex items-center gap-2 justify-center border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white\"\n              >\n                📋 Copy CV Info\n              </button>\n              <a\n                href=\"/Mohamed_Ayman_CV_2025.pdf\"\n                download=\"Mohamed_Ayman_CV_2025.pdf\"\n                className=\"btn btn-primary inline-flex items-center gap-2 justify-center\"\n              >\n                <FaDownload />\n                Download CV (PDF)\n              </a>\n              <button\n                onClick={() => {\n                  try {\n                    // Create a simple CV page\n                    const newWindow = window.open(\n                      \"\",\n                      \"_blank\",\n                      \"width=800,height=1000,scrollbars=yes,resizable=yes\"\n                    );\n\n                    if (!newWindow) {\n                      alert(\"Please allow popups for this site to view the CV\");\n                      return;\n                    }\n\n                    newWindow.document.write(`\n                    <!DOCTYPE html>\n                    <html>\n                    <head>\n                      <title>Mohamed Ayman - CV</title>\n                      <meta charset=\"UTF-8\">\n                      <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n                      <script src=\"https://cdn.tailwindcss.com\"></meta>\n                      <link href=\"https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap\" rel=\"stylesheet\">\n                      <style>\n                        body {\n                          font-family: 'Poppins', sans-serif;\n                          background-color: #f8fafc;\n                        }\n                        .cv-container {\n                          max-width: 210mm;\n                          margin: 20px auto;\n                          background: white;\n                          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n                          border-radius: 8px;\n                          overflow: hidden;\n                        }\n                        @media print {\n                          @page {\n                            margin: 0.5in;\n                            size: A4;\n                          }\n                          body {\n                            font-size: 12pt;\n                            line-height: 1.4;\n                            background: white !important;\n                          }\n                          .cv-container {\n                            box-shadow: none !important;\n                            margin: 0 !important;\n                            border-radius: 0 !important;\n                          }\n                          .print-hidden {\n                            display: none !important;\n                          }\n                          .page-break {\n                            page-break-before: always;\n                          }\n                        }\n                        .section-border {\n                          border-bottom: 2px solid #3b82f6;\n                          padding-bottom: 4px;\n                          margin-bottom: 12px;\n                        }\n                        .skill-item {\n                          background: #f1f5f9;\n                          padding: 4px 8px;\n                          border-radius: 4px;\n                          font-size: 0.875rem;\n                        }\n                      </style>\n                    </head>\n                    <body>\n                      <div class=\"cv-container\">\n                        <div class=\"print-hidden p-4 bg-gray-100 border-b\">\n                          <div class=\"flex justify-between items-center\">\n                            <h3 class=\"text-lg font-semibold text-gray-800\">Mohamed Ayman - CV</h3>\n                            <button onclick=\"window.print()\" class=\"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\">\n                              🖨️ Print / Save as PDF\n                            </button>\n                          </div>\n                        </div>\n\n                        <div class=\"p-8\">\n                          <!-- Header -->\n                          <div class=\"text-center mb-8 pb-6 border-b-2 border-blue-600\">\n                            <h1 class=\"text-4xl font-bold text-gray-800 mb-2\">Mohamed Ayman</h1>\n                            <h2 class=\"text-xl text-blue-600 font-semibold mb-4\">Backend Developer</h2>\n                            <div class=\"flex flex-wrap justify-center gap-6 text-sm text-gray-600\">\n                              <div class=\"flex items-center gap-2\">\n                                <span class=\"text-blue-600\">📧</span>\n                                <span><EMAIL></span>\n                              </div>\n                              <div class=\"flex items-center gap-2\">\n                                <span class=\"text-blue-600\">📱</span>\n                                <span>+01018551242</span>\n                              </div>\n                              <div class=\"flex items-center gap-2\">\n                                <span class=\"text-blue-600\">📍</span>\n                                <span>Assiut, Egypt</span>\n                              </div>\n                            </div>\n                          </div>\n\n                          <!-- Professional Summary -->\n                          <section class=\"mb-8\">\n                            <h3 class=\"section-border text-xl font-bold text-gray-800\">Professional Summary</h3>\n                            <div class=\"bg-gray-50 p-4 rounded-lg\">\n                              <p class=\"text-gray-700 leading-relaxed\">\n                                Passionate Backend Developer with expertise in creating full-stack web applications.\n                                Specialized in building robust e-commerce platforms and have successfully developed\n                                <strong>WebDecor&More</strong>, a comprehensive home decor platform handling 100+ product listings with\n                                optimized performance. Improved system response time by <strong>40%</strong> using optimized backend\n                                queries in Node.js.\n                              </p>\n                            </div>\n                          </section>\n\n                          <!-- Technical Skills -->\n                          <section class=\"mb-8\">\n                            <h3 class=\"section-border text-xl font-bold text-gray-800\">Technical Skills</h3>\n                            <div class=\"grid grid-cols-2 gap-6\">\n                              <div class=\"bg-blue-50 p-4 rounded-lg\">\n                                <h4 class=\"font-bold text-blue-800 mb-3 text-center\">Backend Technologies</h4>\n                                <div class=\"space-y-2\">\n                                  <div class=\"skill-item text-center\">Node.js & Express.js</div>\n                                  <div class=\"skill-item text-center\">MongoDB & Database Design</div>\n                                  <div class=\"skill-item text-center\">RESTful APIs Development</div>\n                                  <div class=\"skill-item text-center\">Authentication & Authorization</div>\n                                </div>\n                              </div>\n                              <div class=\"bg-green-50 p-4 rounded-lg\">\n                                <h4 class=\"font-bold text-green-800 mb-3 text-center\">Frontend Technologies</h4>\n                                <div class=\"space-y-2\">\n                                  <div class=\"skill-item text-center\">HTML5, CSS3, JavaScript</div>\n                                  <div class=\"skill-item text-center\">React.js & Modern Frameworks</div>\n                                  <div class=\"skill-item text-center\">Responsive Web Design</div>\n                                  <div class=\"skill-item text-center\">Tailwind CSS</div>\n                                </div>\n                              </div>\n                            </div>\n                          </section>\n\n                          <!-- Key Projects -->\n                          <section class=\"mb-8\">\n                            <h3 class=\"section-border text-xl font-bold text-gray-800\">Key Projects</h3>\n                            <div class=\"bg-gradient-to-r from-purple-50 to-pink-50 p-6 rounded-lg border-l-4 border-purple-500\">\n                              <div class=\"flex justify-between items-start mb-3\">\n                                <h4 class=\"text-lg font-bold text-purple-800\">WebDecor&More Project</h4>\n                                <span class=\"bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-semibold\">2024</span>\n                              </div>\n                              <p class=\"text-purple-700 font-medium mb-3\">Full-Stack E-commerce Platform</p>\n                              <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                                <div>\n                                  <h5 class=\"font-semibold text-gray-800 mb-2\">🛠️ Technologies Used:</h5>\n                                  <div class=\"flex flex-wrap gap-2\">\n                                    <span class=\"bg-green-100 text-green-800 px-2 py-1 rounded text-xs\">Node.js</span>\n                                    <span class=\"bg-green-100 text-green-800 px-2 py-1 rounded text-xs\">MongoDB</span>\n                                    <span class=\"bg-green-100 text-green-800 px-2 py-1 rounded text-xs\">Express.js</span>\n                                    <span class=\"bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs\">JavaScript</span>\n                                    <span class=\"bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs\">HTML/CSS</span>\n                                  </div>\n                                </div>\n                                <div>\n                                  <h5 class=\"font-semibold text-gray-800 mb-2\">🎯 Key Achievements:</h5>\n                                  <ul class=\"text-sm text-gray-700 space-y-1\">\n                                    <li>✅ 100+ product listings</li>\n                                    <li>✅ 40% performance improvement</li>\n                                    <li>✅ Full authentication system</li>\n                                    <li>✅ Shopping cart functionality</li>\n                                  </ul>\n                                </div>\n                              </div>\n                            </div>\n                          </section>\n\n                          <!-- Experience -->\n                          <section class=\"mb-8\">\n                            <h3 class=\"section-border text-xl font-bold text-gray-800\">Professional Experience</h3>\n                            <div class=\"bg-gray-50 p-6 rounded-lg border-l-4 border-blue-500\">\n                              <div class=\"flex justify-between items-start mb-3\">\n                                <h4 class=\"text-lg font-bold text-blue-800\">Backend Developer</h4>\n                                <span class=\"bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-semibold\">2021 - Present</span>\n                              </div>\n                              <p class=\"text-blue-700 font-medium mb-4\">Freelance</p>\n                              <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                                <ul class=\"text-sm text-gray-700 space-y-2\">\n                                  <li class=\"flex items-start gap-2\">\n                                    <span class=\"text-green-500 mt-1\">▶</span>\n                                    <span>Developed and maintained backend systems for web applications</span>\n                                  </li>\n                                  <li class=\"flex items-start gap-2\">\n                                    <span class=\"text-green-500 mt-1\">▶</span>\n                                    <span>Implemented secure authentication and authorization systems</span>\n                                  </li>\n                                </ul>\n                                <ul class=\"text-sm text-gray-700 space-y-2\">\n                                  <li class=\"flex items-start gap-2\">\n                                    <span class=\"text-green-500 mt-1\">▶</span>\n                                    <span>Optimized database queries and improved application performance</span>\n                                  </li>\n                                  <li class=\"flex items-start gap-2\">\n                                    <span class=\"text-green-500 mt-1\">▶</span>\n                                    <span>Collaborated with frontend developers for seamless user experiences</span>\n                                  </li>\n                                </ul>\n                              </div>\n                            </div>\n                          </section>\n\n                          <!-- Languages & Education -->\n                          <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8\">\n                            <section>\n                              <h3 class=\"section-border text-xl font-bold text-gray-800\">Languages</h3>\n                              <div class=\"space-y-3\">\n                                <div class=\"bg-green-50 p-3 rounded-lg flex justify-between items-center\">\n                                  <span class=\"font-semibold text-green-800\">Arabic</span>\n                                  <span class=\"bg-green-200 text-green-800 px-3 py-1 rounded-full text-sm font-semibold\">Native</span>\n                                </div>\n                                <div class=\"bg-blue-50 p-3 rounded-lg flex justify-between items-center\">\n                                  <span class=\"font-semibold text-blue-800\">English</span>\n                                  <span class=\"bg-blue-200 text-blue-800 px-3 py-1 rounded-full text-sm font-semibold\">Professional</span>\n                                </div>\n                              </div>\n                            </section>\n\n                            <section>\n                              <h3 class=\"section-border text-xl font-bold text-gray-800\">Education</h3>\n                              <div class=\"bg-yellow-50 p-4 rounded-lg border-l-4 border-yellow-500\">\n                                <h4 class=\"font-bold text-yellow-800 mb-2\">Computer Science</h4>\n                                <p class=\"text-yellow-700 text-sm\">University • Assiut, Egypt</p>\n                              </div>\n                            </section>\n                          </div>\n\n                          <!-- Footer -->\n                          <div class=\"text-center pt-6 border-t-2 border-gray-200\">\n                            <p class=\"text-gray-600 text-sm\">\n                              Thank you for considering my application. I look forward to discussing how I can contribute to your team.\n                            </p>\n                            <div class=\"mt-4 flex justify-center space-x-4 text-sm text-gray-500\">\n                              <span>📧 <EMAIL></span>\n                              <span>📱 +01018551242</span>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    </body>\n                    </html>\n                  `);\n                    newWindow.document.close();\n                  } catch (error) {\n                    console.error(\"Error opening CV:\", error);\n                    alert(\"Error opening CV. Please try again.\");\n                  }\n                }}\n                className=\"btn btn-secondary inline-flex items-center gap-2 justify-center\"\n              >\n                👁️ View CV\n              </button>\n            </div>\n          </div>\n\n          {/* Skills */}\n          <div className=\"animate-on-scroll\" ref={skillsRef}>\n            <h3 className=\"text-2xl md:text-3xl font-bold mb-8 text-gray-800\">\n              My Skills\n            </h3>\n            <div className=\"space-y-6\">\n              {skills.map((skill, index) => (\n                <div key={index} className=\"skill-item\">\n                  <div className=\"flex justify-between items-center mb-2\">\n                    <span className=\"font-medium text-gray-700\">\n                      {skill.name}\n                    </span>\n                    <span className=\"text-sm text-gray-500\">\n                      {skill.percentage}%\n                    </span>\n                  </div>\n                  <div className=\"skill-bar\">\n                    <div\n                      className={`skill-progress transition-all duration-1000 ease-out ${\n                        skillsAnimated ? \"w-full\" : \"w-0\"\n                      }`}\n                      style={{\n                        width: skillsAnimated ? `${skill.percentage}%` : \"0%\",\n                        transitionDelay: `${index * 200}ms`,\n                      }}\n                    ></div>\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            {/* Stats */}\n            <div className=\"grid grid-cols-2 md:grid-cols-3 gap-6 mt-12\">\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-primary-600 mb-2\">\n                  50+\n                </div>\n                <div className=\"text-gray-600\">Projects Completed</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-primary-600 mb-2\">\n                  30+\n                </div>\n                <div className=\"text-gray-600\">Happy Clients</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-primary-600 mb-2\">\n                  3+\n                </div>\n                <div className=\"text-gray-600\">Years Experience</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default About;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,UAAU,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGT,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAMU,SAAS,GAAGR,MAAM,CAAC,IAAI,CAAC;EAE9B,MAAMS,MAAM,GAAG,CACb;IAAEC,IAAI,EAAE,UAAU;IAAEC,UAAU,EAAE;EAAG,CAAC,EACpC;IAAED,IAAI,EAAE,YAAY;IAAEC,UAAU,EAAE;EAAG,CAAC,EACtC;IAAED,IAAI,EAAE,OAAO;IAAEC,UAAU,EAAE;EAAG,CAAC,EACjC;IAAED,IAAI,EAAE,SAAS;IAAEC,UAAU,EAAE;EAAG,CAAC,EACnC;IAAED,IAAI,EAAE,SAAS;IAAEC,UAAU,EAAE;EAAG,CAAC,EACnC;IAAED,IAAI,EAAE,YAAY;IAAEC,UAAU,EAAE;EAAG,CAAC,CACvC;EAED,MAAMC,YAAY,GAAG,CACnB;IAAEC,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAgB,CAAC,EACzC;IAAED,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE;EAA6B,CAAC,EACvD;IAAED,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAe,CAAC,EACzC;IAAED,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAgB,CAAC,EAC7C;IAAED,KAAK,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAW,CAAC,EAC1C;IAAED,KAAK,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAY,CAAC,CAC3C;EAEDf,SAAS,CAAC,MAAM;IACd,MAAMgB,QAAQ,GAAG,IAAIC,oBAAoB,CACtCC,OAAO,IAAK;MACXA,OAAO,CAACC,OAAO,CAAEC,KAAK,IAAK;QACzB,IAAIA,KAAK,CAACC,cAAc,IAAI,CAACd,cAAc,EAAE;UAC3CC,iBAAiB,CAAC,IAAI,CAAC;QACzB;MACF,CAAC,CAAC;IACJ,CAAC,EACD;MAAEc,SAAS,EAAE;IAAI,CACnB,CAAC;IAED,IAAIb,SAAS,CAACc,OAAO,EAAE;MACrBP,QAAQ,CAACQ,OAAO,CAACf,SAAS,CAACc,OAAO,CAAC;IACrC;IAEA,OAAO,MAAM;MACX,IAAId,SAAS,CAACc,OAAO,EAAE;QACrBP,QAAQ,CAACS,SAAS,CAAChB,SAAS,CAACc,OAAO,CAAC;MACvC;IACF,CAAC;EACH,CAAC,EAAE,CAAChB,cAAc,CAAC,CAAC;EAEpB,oBACEH,OAAA;IAASsB,EAAE,EAAC,OAAO;IAACC,SAAS,EAAC,4BAA4B;IAAAC,QAAA,eACxDxB,OAAA;MAAKuB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAE/BxB,OAAA;QAAKuB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCxB,OAAA;UAAIuB,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3C5B,OAAA;UAAGuB,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,eAEN5B,OAAA;QAAKuB,SAAS,EAAC,oDAAoD;QAAAC,QAAA,gBAEjExB,OAAA;UAAKuB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCxB,OAAA;YAAIuB,SAAS,EAAC,mDAAmD;YAAAC,QAAA,EAAC;UAElE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL5B,OAAA;YAAGuB,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAAC;UAQlD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAGJ5B,OAAA;YAAKuB,SAAS,EAAC,4CAA4C;YAAAC,QAAA,EACxDf,YAAY,CAACoB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC5B/B,OAAA;cAEEuB,SAAS,EAAC,iEAAiE;cAAAC,QAAA,gBAE3ExB,OAAA;gBAAMuB,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,GAC1CM,IAAI,CAACpB,KAAK,EAAC,GACd;cAAA;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACP5B,OAAA;gBAAMuB,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAEM,IAAI,CAACnB;cAAK;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA,GAN9CG,KAAK;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOP,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGN5B,OAAA;YAAKuB,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAE9CxB,OAAA;cACEgC,OAAO,EAAEA,CAAA,KAAM;gBACb;gBACA,MAAMC,MAAM,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;;gBAED;gBACA,IAAIC,SAAS,CAACC,SAAS,EAAE;kBACvBD,SAAS,CAACC,SAAS,CAChBC,SAAS,CAACH,MAAM,CAAC,CACjBI,IAAI,CAAC,MAAM;oBACVC,KAAK,CAAC,qCAAqC,CAAC;kBAC9C,CAAC,CAAC,CACDC,KAAK,CAAC,MAAM;oBACXD,KAAK,CAACL,MAAM,CAAC;kBACf,CAAC,CAAC;gBACN,CAAC,MAAM;kBACLK,KAAK,CAACL,MAAM,CAAC;gBACf;cACF,CAAE;cACFV,SAAS,EAAC,kJAAkJ;cAAAC,QAAA,EAC7J;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT5B,OAAA;cACEwC,IAAI,EAAC,4BAA4B;cACjCC,QAAQ,EAAC,2BAA2B;cACpClB,SAAS,EAAC,+DAA+D;cAAAC,QAAA,gBAEzExB,OAAA,CAACF,UAAU;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,qBAEhB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ5B,OAAA;cACEgC,OAAO,EAAEA,CAAA,KAAM;gBACb,IAAI;kBACF;kBACA,MAAMU,SAAS,GAAGC,MAAM,CAACC,IAAI,CAC3B,EAAE,EACF,QAAQ,EACR,oDACF,CAAC;kBAED,IAAI,CAACF,SAAS,EAAE;oBACdJ,KAAK,CAAC,kDAAkD,CAAC;oBACzD;kBACF;kBAEAI,SAAS,CAACG,QAAQ,CAACC,KAAK,CAAC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,CAAC;kBACAJ,SAAS,CAACG,QAAQ,CAACE,KAAK,CAAC,CAAC;gBAC5B,CAAC,CAAC,OAAOC,KAAK,EAAE;kBACdC,OAAO,CAACD,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;kBACzCV,KAAK,CAAC,qCAAqC,CAAC;gBAC9C;cACF,CAAE;cACFf,SAAS,EAAC,iEAAiE;cAAAC,QAAA,EAC5E;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN5B,OAAA;UAAKuB,SAAS,EAAC,mBAAmB;UAAC2B,GAAG,EAAE7C,SAAU;UAAAmB,QAAA,gBAChDxB,OAAA;YAAIuB,SAAS,EAAC,mDAAmD;YAAAC,QAAA,EAAC;UAElE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL5B,OAAA;YAAKuB,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBlB,MAAM,CAACuB,GAAG,CAAC,CAACsB,KAAK,EAAEpB,KAAK,kBACvB/B,OAAA;cAAiBuB,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACrCxB,OAAA;gBAAKuB,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrDxB,OAAA;kBAAMuB,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EACxC2B,KAAK,CAAC5C;gBAAI;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC,eACP5B,OAAA;kBAAMuB,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GACpC2B,KAAK,CAAC3C,UAAU,EAAC,GACpB;gBAAA;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACN5B,OAAA;gBAAKuB,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACxBxB,OAAA;kBACEuB,SAAS,EAAE,wDACTpB,cAAc,GAAG,QAAQ,GAAG,KAAK,EAChC;kBACHiD,KAAK,EAAE;oBACLC,KAAK,EAAElD,cAAc,GAAG,GAAGgD,KAAK,CAAC3C,UAAU,GAAG,GAAG,IAAI;oBACrD8C,eAAe,EAAE,GAAGvB,KAAK,GAAG,GAAG;kBACjC;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GAnBEG,KAAK;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoBV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGN5B,OAAA;YAAKuB,SAAS,EAAC,6CAA6C;YAAAC,QAAA,gBAC1DxB,OAAA;cAAKuB,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BxB,OAAA;gBAAKuB,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN5B,OAAA;gBAAKuB,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eACN5B,OAAA;cAAKuB,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BxB,OAAA;gBAAKuB,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN5B,OAAA;gBAAKuB,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,eACN5B,OAAA;cAAKuB,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BxB,OAAA;gBAAKuB,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN5B,OAAA;gBAAKuB,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAAC1B,EAAA,CAjdID,KAAK;AAAAsD,EAAA,GAALtD,KAAK;AAmdX,eAAeA,KAAK;AAAC,IAAAsD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}