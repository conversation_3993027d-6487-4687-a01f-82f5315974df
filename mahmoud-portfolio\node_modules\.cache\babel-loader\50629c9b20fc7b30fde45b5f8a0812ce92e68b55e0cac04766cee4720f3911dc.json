{"ast": null, "code": "var _jsxFileName = \"E:\\\\test_refactor\\\\portofolio\\\\mahmoud-portfolio\\\\src\\\\components\\\\Navbar.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { FaBars, FaTimes } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Navbar = () => {\n  _s();\n  const [isOpen, setIsOpen] = useState(false);\n  const [scrolled, setScrolled] = useState(false);\n  useEffect(() => {\n    const handleScroll = () => {\n      const isScrolled = window.scrollY > 100;\n      setScrolled(isScrolled);\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n  const toggleMenu = () => {\n    setIsOpen(!isOpen);\n  };\n  const closeMenu = () => {\n    setIsOpen(false);\n  };\n  const scrollToSection = sectionId => {\n    const element = document.getElementById(sectionId);\n    if (element) {\n      element.scrollIntoView({\n        behavior: 'smooth'\n      });\n    }\n    closeMenu();\n  };\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: `fixed top-0 w-full z-50 transition-all duration-300 ${scrolled ? 'bg-white bg-opacity-95 backdrop-blur-md shadow-lg' : 'bg-white bg-opacity-90 backdrop-blur-sm'}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container-custom\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center py-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-2xl font-bold text-primary-600\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => scrollToSection('home'),\n            children: \"MA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden md:flex space-x-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => scrollToSection('home'),\n            className: \"nav-link\",\n            children: \"Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => scrollToSection('about'),\n            className: \"nav-link\",\n            children: \"About\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => scrollToSection('services'),\n            className: \"nav-link\",\n            children: \"Services\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => scrollToSection('portfolio'),\n            className: \"nav-link\",\n            children: \"Portfolio\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => scrollToSection('contact'),\n            className: \"nav-link\",\n            children: \"Contact\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"md:hidden\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: toggleMenu,\n            className: \"text-gray-700 hover:text-primary-600 transition-colors duration-300\",\n            children: isOpen ? /*#__PURE__*/_jsxDEV(FaTimes, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 25\n            }, this) : /*#__PURE__*/_jsxDEV(FaBars, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 49\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `md:hidden transition-all duration-300 overflow-hidden ${isOpen ? 'max-h-64 opacity-100' : 'max-h-0 opacity-0'}`,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"py-4 space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => scrollToSection('home'),\n            className: \"block w-full text-left nav-link py-2\",\n            children: \"Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => scrollToSection('about'),\n            className: \"block w-full text-left nav-link py-2\",\n            children: \"About\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => scrollToSection('services'),\n            className: \"block w-full text-left nav-link py-2\",\n            children: \"Services\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => scrollToSection('portfolio'),\n            className: \"block w-full text-left nav-link py-2\",\n            children: \"Portfolio\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => scrollToSection('contact'),\n            className: \"block w-full text-left nav-link py-2\",\n            children: \"Contact\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 5\n  }, this);\n};\n_s(Navbar, \"E9hQXvWoSqP64D0yGnqXyHKCBFc=\");\n_c = Navbar;\nexport default Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "FaBars", "FaTimes", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON>", "_s", "isOpen", "setIsOpen", "scrolled", "setScrolled", "handleScroll", "isScrolled", "window", "scrollY", "addEventListener", "removeEventListener", "toggleMenu", "closeMenu", "scrollToSection", "sectionId", "element", "document", "getElementById", "scrollIntoView", "behavior", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "_c", "$RefreshReg$"], "sources": ["E:/test_refactor/portofolio/mahmoud-portfolio/src/components/Navbar.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { FaBars, FaTimes } from 'react-icons/fa';\n\nconst Navbar = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [scrolled, setScrolled] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      const isScrolled = window.scrollY > 100;\n      setScrolled(isScrolled);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const toggleMenu = () => {\n    setIsOpen(!isOpen);\n  };\n\n  const closeMenu = () => {\n    setIsOpen(false);\n  };\n\n  const scrollToSection = (sectionId) => {\n    const element = document.getElementById(sectionId);\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n    }\n    closeMenu();\n  };\n\n  return (\n    <nav className={`fixed top-0 w-full z-50 transition-all duration-300 ${\n      scrolled \n        ? 'bg-white bg-opacity-95 backdrop-blur-md shadow-lg' \n        : 'bg-white bg-opacity-90 backdrop-blur-sm'\n    }`}>\n      <div className=\"container-custom\">\n        <div className=\"flex justify-between items-center py-4\">\n          {/* Logo */}\n          <div className=\"text-2xl font-bold text-primary-600\">\n            <button onClick={() => scrollToSection('home')}>\n              MA\n            </button>\n          </div>\n\n          {/* Desktop Menu */}\n          <div className=\"hidden md:flex space-x-8\">\n            <button onClick={() => scrollToSection('home')} className=\"nav-link\">\n              Home\n            </button>\n            <button onClick={() => scrollToSection('about')} className=\"nav-link\">\n              About\n            </button>\n            <button onClick={() => scrollToSection('services')} className=\"nav-link\">\n              Services\n            </button>\n            <button onClick={() => scrollToSection('portfolio')} className=\"nav-link\">\n              Portfolio\n            </button>\n            <button onClick={() => scrollToSection('contact')} className=\"nav-link\">\n              Contact\n            </button>\n          </div>\n\n          {/* Mobile Menu Button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={toggleMenu}\n              className=\"text-gray-700 hover:text-primary-600 transition-colors duration-300\"\n            >\n              {isOpen ? <FaTimes size={24} /> : <FaBars size={24} />}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Menu */}\n        <div className={`md:hidden transition-all duration-300 overflow-hidden ${\n          isOpen ? 'max-h-64 opacity-100' : 'max-h-0 opacity-0'\n        }`}>\n          <div className=\"py-4 space-y-4\">\n            <button \n              onClick={() => scrollToSection('home')} \n              className=\"block w-full text-left nav-link py-2\"\n            >\n              Home\n            </button>\n            <button \n              onClick={() => scrollToSection('about')} \n              className=\"block w-full text-left nav-link py-2\"\n            >\n              About\n            </button>\n            <button \n              onClick={() => scrollToSection('services')} \n              className=\"block w-full text-left nav-link py-2\"\n            >\n              Services\n            </button>\n            <button \n              onClick={() => scrollToSection('portfolio')} \n              className=\"block w-full text-left nav-link py-2\"\n            >\n              Portfolio\n            </button>\n            <button \n              onClick={() => scrollToSection('contact')} \n              className=\"block w-full text-left nav-link py-2\"\n            >\n              Contact\n            </button>\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n};\n\nexport default Navbar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,OAAO,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGT,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACU,QAAQ,EAAEC,WAAW,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EAE/CC,SAAS,CAAC,MAAM;IACd,MAAMW,YAAY,GAAGA,CAAA,KAAM;MACzB,MAAMC,UAAU,GAAGC,MAAM,CAACC,OAAO,GAAG,GAAG;MACvCJ,WAAW,CAACE,UAAU,CAAC;IACzB,CAAC;IAEDC,MAAM,CAACE,gBAAgB,CAAC,QAAQ,EAAEJ,YAAY,CAAC;IAC/C,OAAO,MAAME,MAAM,CAACG,mBAAmB,CAAC,QAAQ,EAAEL,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMM,UAAU,GAAGA,CAAA,KAAM;IACvBT,SAAS,CAAC,CAACD,MAAM,CAAC;EACpB,CAAC;EAED,MAAMW,SAAS,GAAGA,CAAA,KAAM;IACtBV,SAAS,CAAC,KAAK,CAAC;EAClB,CAAC;EAED,MAAMW,eAAe,GAAIC,SAAS,IAAK;IACrC,MAAMC,OAAO,GAAGC,QAAQ,CAACC,cAAc,CAACH,SAAS,CAAC;IAClD,IAAIC,OAAO,EAAE;MACXA,OAAO,CAACG,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IAChD;IACAP,SAAS,CAAC,CAAC;EACb,CAAC;EAED,oBACEd,OAAA;IAAKsB,SAAS,EAAE,uDACdjB,QAAQ,GACJ,mDAAmD,GACnD,yCAAyC,EAC5C;IAAAkB,QAAA,eACDvB,OAAA;MAAKsB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BvB,OAAA;QAAKsB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBAErDvB,OAAA;UAAKsB,SAAS,EAAC,qCAAqC;UAAAC,QAAA,eAClDvB,OAAA;YAAQwB,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,MAAM,CAAE;YAAAQ,QAAA,EAAC;UAEhD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGN5B,OAAA;UAAKsB,SAAS,EAAC,0BAA0B;UAAAC,QAAA,gBACvCvB,OAAA;YAAQwB,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,MAAM,CAAE;YAACO,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAC;UAErE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT5B,OAAA;YAAQwB,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,OAAO,CAAE;YAACO,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAC;UAEtE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT5B,OAAA;YAAQwB,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,UAAU,CAAE;YAACO,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAC;UAEzE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT5B,OAAA;YAAQwB,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,WAAW,CAAE;YAACO,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAC;UAE1E;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT5B,OAAA;YAAQwB,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,SAAS,CAAE;YAACO,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAC;UAExE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGN5B,OAAA;UAAKsB,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxBvB,OAAA;YACEwB,OAAO,EAAEX,UAAW;YACpBS,SAAS,EAAC,qEAAqE;YAAAC,QAAA,EAE9EpB,MAAM,gBAAGH,OAAA,CAACF,OAAO;cAAC+B,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG5B,OAAA,CAACH,MAAM;cAACgC,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN5B,OAAA;QAAKsB,SAAS,EAAE,yDACdnB,MAAM,GAAG,sBAAsB,GAAG,mBAAmB,EACpD;QAAAoB,QAAA,eACDvB,OAAA;UAAKsB,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BvB,OAAA;YACEwB,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,MAAM,CAAE;YACvCO,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EACjD;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT5B,OAAA;YACEwB,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,OAAO,CAAE;YACxCO,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EACjD;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT5B,OAAA;YACEwB,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,UAAU,CAAE;YAC3CO,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EACjD;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT5B,OAAA;YACEwB,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,WAAW,CAAE;YAC5CO,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EACjD;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT5B,OAAA;YACEwB,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAAC,SAAS,CAAE;YAC1CO,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EACjD;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1B,EAAA,CAnHID,MAAM;AAAA6B,EAAA,GAAN7B,MAAM;AAqHZ,eAAeA,MAAM;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}