@tailwind base;
@tailwind components;
@tailwind utilities;

@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap");

@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    font-family: "Poppins", sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
  }
}

@layer components {
  .btn {
    @apply px-8 py-3 rounded-full font-semibold transition-all duration-300 cursor-pointer inline-block text-center;
  }

  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 hover:-translate-y-1 hover:shadow-lg;
  }

  .btn-secondary {
    @apply bg-transparent text-white border-2 border-white hover:bg-white hover:text-primary-600;
  }

  .section-padding {
    @apply py-20 lg:py-24;
  }

  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .section-title {
    @apply text-3xl md:text-4xl font-bold text-center mb-4;
  }

  .section-subtitle {
    @apply text-lg text-gray-600 text-center mb-12;
  }

  .card {
    @apply bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2;
  }

  .social-link {
    @apply w-12 h-12 rounded-full bg-white bg-opacity-10 flex items-center justify-center text-white hover:bg-primary-600 hover:-translate-y-1 transition-all duration-300;
  }

  .nav-link {
    @apply text-gray-700 font-medium hover:text-primary-600 transition-colors duration-300 relative;
  }

  .nav-link::after {
    content: "";
    @apply absolute w-0 h-0.5 bottom-0 left-0 bg-primary-600 transition-all duration-300;
  }

  .nav-link:hover::after {
    @apply w-full;
  }

  .skill-bar {
    @apply h-2 bg-gray-200 rounded-full overflow-hidden;
  }

  .skill-progress {
    @apply h-full bg-gradient-to-r from-primary-600 to-secondary-600 rounded-full transition-all duration-1000 ease-out;
  }

  .portfolio-overlay {
    @apply absolute inset-0 bg-primary-600 bg-opacity-90 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-300;
  }

  .form-input {
    @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-primary-600 transition-colors duration-300;
  }

  .animate-on-scroll {
    @apply opacity-0 translate-y-8 transition-all duration-700 ease-out;
  }

  .animate-on-scroll.visible {
    @apply opacity-100 translate-y-0;
  }

  /* Portfolio animations */
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  .portfolio-item-enter {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }

  .portfolio-item-enter-active {
    opacity: 1;
    transform: translateY(0) scale(1);
    transition: all 0.5s ease-in-out;
  }

  /* Print Styles */
  @media print {
    @page {
      margin: 0.5in;
    }

    body {
      font-size: 12pt;
      line-height: 1.4;
    }

    .print\\:hidden {
      display: none !important;
    }

    .print\\:shadow-none {
      box-shadow: none !important;
    }

    .print\\:p-6 {
      padding: 1.5rem !important;
    }

    h1 {
      font-size: 24pt;
    }

    h2 {
      font-size: 18pt;
    }

    h3 {
      font-size: 14pt;
    }

    h4 {
      font-size: 12pt;
    }
  }
}
