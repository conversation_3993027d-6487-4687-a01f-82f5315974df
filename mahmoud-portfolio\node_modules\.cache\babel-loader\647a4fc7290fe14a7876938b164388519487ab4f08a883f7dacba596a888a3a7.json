{"ast": null, "code": "var _jsxFileName = \"E:\\\\test_refactor\\\\portofolio\\\\mahmoud-portfolio\\\\src\\\\components\\\\Services.js\";\nimport React from 'react';\nimport { FaCode, FaMobile, FaPaintBrush, FaRocket, FaDatabase, FaSearch } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Services = () => {\n  const services = [{\n    icon: /*#__PURE__*/_jsxDEV(FaCode, {\n      size: 40\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 13\n    }, this),\n    title: 'Web Development',\n    description: 'Creating responsive and modern websites using the latest technologies and best practices.'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(FaMobile, {\n      size: 40\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 13\n    }, this),\n    title: 'Responsive Design',\n    description: 'Ensuring your website looks great and functions perfectly on all devices and screen sizes.'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(FaPaintBrush, {\n      size: 40\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 13\n    }, this),\n    title: 'UI/UX Design',\n    description: 'Designing intuitive and beautiful user interfaces that provide excellent user experiences.'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(FaRocket, {\n      size: 40\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 13\n    }, this),\n    title: 'Performance Optimization',\n    description: 'Optimizing websites for speed and performance to ensure the best user experience.'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(FaDatabase, {\n      size: 40\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 13\n    }, this),\n    title: 'Backend Integration',\n    description: 'Seamlessly connecting frontend applications with backend services and APIs.'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(FaSearch, {\n      size: 40\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 13\n    }, this),\n    title: 'SEO Optimization',\n    description: 'Implementing SEO best practices to improve your website\\'s search engine visibility.'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"services\",\n    className: \"section-padding\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container-custom\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title\",\n          children: \"My Services\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"section-subtitle\",\n          children: \"What I can do for you\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n        children: services.map((service, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card p-8 text-center animate-on-scroll group\",\n          style: {\n            animationDelay: `${index * 100}ms`\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-20 h-20 mx-auto mb-6 bg-gradient-to-r from-primary-600 to-secondary-600 rounded-full flex items-center justify-center text-white group-hover:scale-110 transition-transform duration-300\",\n            children: service.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-semibold mb-4 text-gray-800\",\n            children: service.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 leading-relaxed\",\n            children: service.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-6 opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-1 bg-gradient-to-r from-primary-600 to-secondary-600 mx-auto rounded-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mt-16\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-r from-primary-600 to-secondary-600 rounded-2xl p-8 md:p-12 text-white\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-2xl md:text-3xl font-bold mb-4\",\n            children: \"Ready to Start Your Project?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg mb-8 opacity-90 max-w-2xl mx-auto\",\n            children: \"Let's work together to bring your ideas to life. I'm here to help you create amazing digital experiences that your users will love.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              const element = document.getElementById('contact');\n              if (element) {\n                element.scrollIntoView({\n                  behavior: 'smooth'\n                });\n              }\n            },\n            className: \"bg-white text-primary-600 px-8 py-3 rounded-full font-semibold hover:bg-gray-100 transition-colors duration-300\",\n            children: \"Get Started\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 5\n  }, this);\n};\n_c = Services;\nexport default Services;\nvar _c;\n$RefreshReg$(_c, \"Services\");", "map": {"version": 3, "names": ["React", "FaCode", "FaMobile", "FaPaintBrush", "FaRocket", "FaDatabase", "FaSearch", "jsxDEV", "_jsxDEV", "Services", "services", "icon", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "description", "id", "className", "children", "map", "service", "index", "style", "animationDelay", "onClick", "element", "document", "getElementById", "scrollIntoView", "behavior", "_c", "$RefreshReg$"], "sources": ["E:/test_refactor/portofolio/mahmoud-portfolio/src/components/Services.js"], "sourcesContent": ["import React from 'react';\nimport { FaCode, FaMobile, FaPaintBrush, FaRocket, FaDatabase, FaSearch } from 'react-icons/fa';\n\nconst Services = () => {\n  const services = [\n    {\n      icon: <FaCode size={40} />,\n      title: 'Web Development',\n      description: 'Creating responsive and modern websites using the latest technologies and best practices.',\n    },\n    {\n      icon: <FaMobile size={40} />,\n      title: 'Responsive Design',\n      description: 'Ensuring your website looks great and functions perfectly on all devices and screen sizes.',\n    },\n    {\n      icon: <FaPaintBrush size={40} />,\n      title: 'UI/UX Design',\n      description: 'Designing intuitive and beautiful user interfaces that provide excellent user experiences.',\n    },\n    {\n      icon: <FaRocket size={40} />,\n      title: 'Performance Optimization',\n      description: 'Optimizing websites for speed and performance to ensure the best user experience.',\n    },\n    {\n      icon: <FaDatabase size={40} />,\n      title: 'Backend Integration',\n      description: 'Seamlessly connecting frontend applications with backend services and APIs.',\n    },\n    {\n      icon: <FaSearch size={40} />,\n      title: 'SEO Optimization',\n      description: 'Implementing SEO best practices to improve your website\\'s search engine visibility.',\n    },\n  ];\n\n  return (\n    <section id=\"services\" className=\"section-padding\">\n      <div className=\"container-custom\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"section-title\">My Services</h2>\n          <p className=\"section-subtitle\">What I can do for you</p>\n        </div>\n\n        {/* Services Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {services.map((service, index) => (\n            <div \n              key={index} \n              className=\"card p-8 text-center animate-on-scroll group\"\n              style={{ animationDelay: `${index * 100}ms` }}\n            >\n              {/* Icon */}\n              <div className=\"w-20 h-20 mx-auto mb-6 bg-gradient-to-r from-primary-600 to-secondary-600 rounded-full flex items-center justify-center text-white group-hover:scale-110 transition-transform duration-300\">\n                {service.icon}\n              </div>\n\n              {/* Title */}\n              <h3 className=\"text-xl font-semibold mb-4 text-gray-800\">\n                {service.title}\n              </h3>\n\n              {/* Description */}\n              <p className=\"text-gray-600 leading-relaxed\">\n                {service.description}\n              </p>\n\n              {/* Hover Effect */}\n              <div className=\"mt-6 opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n                <div className=\"w-12 h-1 bg-gradient-to-r from-primary-600 to-secondary-600 mx-auto rounded-full\"></div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Call to Action */}\n        <div className=\"text-center mt-16\">\n          <div className=\"bg-gradient-to-r from-primary-600 to-secondary-600 rounded-2xl p-8 md:p-12 text-white\">\n            <h3 className=\"text-2xl md:text-3xl font-bold mb-4\">\n              Ready to Start Your Project?\n            </h3>\n            <p className=\"text-lg mb-8 opacity-90 max-w-2xl mx-auto\">\n              Let's work together to bring your ideas to life. I'm here to help you create \n              amazing digital experiences that your users will love.\n            </p>\n            <button \n              onClick={() => {\n                const element = document.getElementById('contact');\n                if (element) {\n                  element.scrollIntoView({ behavior: 'smooth' });\n                }\n              }}\n              className=\"bg-white text-primary-600 px-8 py-3 rounded-full font-semibold hover:bg-gray-100 transition-colors duration-300\"\n            >\n              Get Started\n            </button>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Services;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhG,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EACrB,MAAMC,QAAQ,GAAG,CACf;IACEC,IAAI,eAAEH,OAAA,CAACP,MAAM;MAACW,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,IAAI,eAAEH,OAAA,CAACN,QAAQ;MAACU,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC5BC,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,IAAI,eAAEH,OAAA,CAACL,YAAY;MAACS,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAChCC,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,IAAI,eAAEH,OAAA,CAACJ,QAAQ;MAACQ,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC5BC,KAAK,EAAE,0BAA0B;IACjCC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,IAAI,eAAEH,OAAA,CAACH,UAAU;MAACO,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC9BC,KAAK,EAAE,qBAAqB;IAC5BC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,IAAI,eAAEH,OAAA,CAACF,QAAQ;MAACM,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC5BC,KAAK,EAAE,kBAAkB;IACzBC,WAAW,EAAE;EACf,CAAC,CACF;EAED,oBACEV,OAAA;IAASW,EAAE,EAAC,UAAU;IAACC,SAAS,EAAC,iBAAiB;IAAAC,QAAA,eAChDb,OAAA;MAAKY,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAE/Bb,OAAA;QAAKY,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCb,OAAA;UAAIY,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAW;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9CR,OAAA;UAAGY,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAC;QAAqB;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC,eAGNR,OAAA;QAAKY,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAClEX,QAAQ,CAACY,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC3BhB,OAAA;UAEEY,SAAS,EAAC,8CAA8C;UACxDK,KAAK,EAAE;YAAEC,cAAc,EAAE,GAAGF,KAAK,GAAG,GAAG;UAAK,CAAE;UAAAH,QAAA,gBAG9Cb,OAAA;YAAKY,SAAS,EAAC,4LAA4L;YAAAC,QAAA,EACxME,OAAO,CAACZ;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAGNR,OAAA;YAAIY,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EACrDE,OAAO,CAACN;UAAK;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eAGLR,OAAA;YAAGY,SAAS,EAAC,+BAA+B;YAAAC,QAAA,EACzCE,OAAO,CAACL;UAAW;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eAGJR,OAAA;YAAKY,SAAS,EAAC,wEAAwE;YAAAC,QAAA,eACrFb,OAAA;cAAKY,SAAS,EAAC;YAAkF;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrG,CAAC;QAAA,GAtBDQ,KAAK;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAuBP,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNR,OAAA;QAAKY,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChCb,OAAA;UAAKY,SAAS,EAAC,uFAAuF;UAAAC,QAAA,gBACpGb,OAAA;YAAIY,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAEpD;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLR,OAAA;YAAGY,SAAS,EAAC,2CAA2C;YAAAC,QAAA,EAAC;UAGzD;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJR,OAAA;YACEmB,OAAO,EAAEA,CAAA,KAAM;cACb,MAAMC,OAAO,GAAGC,QAAQ,CAACC,cAAc,CAAC,SAAS,CAAC;cAClD,IAAIF,OAAO,EAAE;gBACXA,OAAO,CAACG,cAAc,CAAC;kBAAEC,QAAQ,EAAE;gBAAS,CAAC,CAAC;cAChD;YACF,CAAE;YACFZ,SAAS,EAAC,iHAAiH;YAAAC,QAAA,EAC5H;UAED;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACiB,EAAA,GApGIxB,QAAQ;AAsGd,eAAeA,QAAQ;AAAC,IAAAwB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}