import React, { useState } from "react";
import { FaGithub, FaExternalLinkAlt } from "react-icons/fa";

const Portfolio = () => {
  const [activeFilter, setActiveFilter] = useState("all");
  const [isTransitioning, setIsTransitioning] = useState(false);

  const handleFilterChange = (filterId) => {
    if (filterId === activeFilter) return;

    setIsTransitioning(true);
    setTimeout(() => {
      setActiveFilter(filterId);
      setTimeout(() => {
        setIsTransitioning(false);
      }, 50);
    }, 150);
  };

  const projects = [
    {
      id: 1,
      title: "WebDecor&More Project",
      category: "web",
      image: "/decore.png",
      description:
        "Full-stack e-commerce platform for home decor with 100+ product listings and optimized performance",
      technologies: [
        "Node.js",
        "MongoDB",
        "Express.js",
        "HTML",
        "CSS",
        "JavaScript",
        "Stripe",
      ],
      liveUrl: "#",
      githubUrl: "#",
    },
    {
      id: 2,
      title: "Mobile App UI",
      category: "mobile",
      image: "/mobile.png",
      description: "Beautiful mobile app interface design",
      technologies: ["React Native", "mongoDB"],
      liveUrl: "#",
      githubUrl: "#",
    },
    {
      id: 3,
      title: "Dashboard Application",
      category: "web",
      image: "/dashboard.png",
      description: "Admin dashboard with analytics and data visualization",
      technologies: ["JavaScript", "Express", "MongoDB"],
      liveUrl: "#",
      githubUrl: "#",
    },
    {
      id: 4,
      title: "Brand Identity",
      category: "design",
      image: "/Brand.png",
      description: "Complete brand identity design package",
      technologies: ["Figma"],
      liveUrl: "#",
      githubUrl: "#",
    },
    {
      id: 5,
      title: "Restaurant Website",
      category: "web",
      image: "/restrant.png",
      description: "Responsive restaurant website with online ordering",
      technologies: ["React", "Tailwind CSS", "HTML"],
      liveUrl: "#",
      githubUrl: "#",
    },
    {
      id: 6,
      title: "Portfolio Website",
      category: "design",
      image: "/portofolio.png",
      description: "Creative portfolio website design",
      technologies: ["React", "Tailwind CSS"],
      liveUrl: "#",
      githubUrl: "#",
    },
  ];

  const filters = [
    { id: "all", label: "All" },
    { id: "web", label: "Web Apps" },
    { id: "mobile", label: "Mobile" },
    { id: "design", label: "Design" },
  ];

  const filteredProjects =
    activeFilter === "all"
      ? projects
      : projects.filter((project) => project.category === activeFilter);

  return (
    <section id="portfolio" className="section-padding bg-gray-50">
      <div className="container-custom">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="section-title">My Portfolio</h2>
          <p className="section-subtitle">Check out my recent work</p>
        </div>

        {/* Filter Buttons */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {filters.map((filter) => (
            <button
              key={filter.id}
              onClick={() => handleFilterChange(filter.id)}
              className={`px-6 py-2 rounded-full font-medium transition-all duration-300 ${
                activeFilter === filter.id
                  ? "bg-primary-600 text-white shadow-lg"
                  : "bg-white text-gray-600 hover:bg-primary-600 hover:text-white"
              }`}
            >
              {filter.label}
            </button>
          ))}
        </div>

        {/* Projects Grid */}
        <div
          className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 transition-opacity duration-300 ${
            isTransitioning ? "opacity-50" : "opacity-100"
          }`}
        >
          {filteredProjects.length === 0 ? (
            <div className="col-span-full text-center py-12">
              <p className="text-gray-500 text-lg">
                No projects found for this category.
              </p>
            </div>
          ) : (
            filteredProjects.map((project, index) => (
              <div
                key={`${activeFilter}-${project.id}`}
                className="card overflow-hidden group"
                style={{
                  animation: `fadeIn 0.5s ease-in-out ${index * 0.1}s both`,
                }}
              >
                {/* Project Image */}
                <div className="relative overflow-hidden">
                  <img
                    src={project.image}
                    alt={project.title}
                    className="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-500"
                  />

                  {/* Overlay */}
                  <div className="portfolio-overlay">
                    <div className="text-center text-white">
                      <h3 className="text-xl font-semibold mb-2">
                        {project.title}
                      </h3>
                      <p className="mb-4 opacity-90">{project.description}</p>
                      <div className="flex gap-4 justify-center">
                        <a
                          href={project.liveUrl}
                          className="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center hover:bg-opacity-30 transition-all duration-300"
                          aria-label="View Live"
                        >
                          <FaExternalLinkAlt size={16} />
                        </a>
                        <a
                          href={project.githubUrl}
                          className="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center hover:bg-opacity-30 transition-all duration-300"
                          aria-label="View Code"
                        >
                          <FaGithub size={16} />
                        </a>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Project Info */}
                <div className="p-6">
                  <h3 className="text-xl font-semibold mb-2 text-gray-800">
                    {project.title}
                  </h3>
                  <p className="text-gray-600 mb-4">{project.description}</p>

                  {/* Technologies */}
                  <div className="flex flex-wrap gap-2">
                    {project.technologies.map((tech, techIndex) => (
                      <span
                        key={techIndex}
                        className="px-3 py-1 bg-primary-100 text-primary-600 text-sm rounded-full"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

        {/* View More Button */}
        <div className="text-center mt-12">
          <button className="btn btn-primary">View More Projects</button>
        </div>
      </div>
    </section>
  );
};

export default Portfolio;
