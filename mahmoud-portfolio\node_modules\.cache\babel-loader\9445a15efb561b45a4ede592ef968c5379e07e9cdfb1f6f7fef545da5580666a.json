{"ast": null, "code": "var _jsxFileName = \"E:\\\\test_refactor\\\\portofolio\\\\mahmoud-portfolio\\\\src\\\\components\\\\Hero.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { <PERSON>aG<PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaInstagram } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Hero = () => {\n  _s();\n  const [displayText, setDisplayText] = useState('');\n  const fullText = 'B Developer';\n  const typingSpeed = 100;\n  useEffect(() => {\n    let index = 0;\n    const timer = setInterval(() => {\n      if (index < fullText.length) {\n        setDisplayText(fullText.slice(0, index + 1));\n        index++;\n      } else {\n        clearInterval(timer);\n      }\n    }, typingSpeed);\n    return () => clearInterval(timer);\n  }, []);\n  const scrollToSection = sectionId => {\n    const element = document.getElementById(sectionId);\n    if (element) {\n      element.scrollIntoView({\n        behavior: 'smooth'\n      });\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"home\",\n    className: \"min-h-screen flex items-center bg-hero-pattern text-white pt-20\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container-custom\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center lg:text-left\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-4xl md:text-5xl lg:text-6xl font-bold mb-4 leading-tight\",\n            children: [\"Hi, I'm \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-yellow-400\",\n              children: \"Mahmoud Rady\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 23\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl md:text-2xl lg:text-3xl mb-6 opacity-90 min-h-[2rem]\",\n            children: [displayText, /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"animate-pulse\",\n              children: \"|\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg md:text-xl mb-8 opacity-80 max-w-2xl mx-auto lg:mx-0\",\n            children: \"I create beautiful and functional web experiences with modern technologies.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col sm:flex-row gap-4 justify-center lg:justify-start mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => scrollToSection('portfolio'),\n              className: \"btn btn-primary\",\n              children: \"View My Work\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => scrollToSection('contact'),\n              className: \"btn btn-secondary\",\n              children: \"Get In Touch\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex gap-4 justify-center lg:justify-start\",\n            children: [/*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              className: \"social-link\",\n              \"aria-label\": \"GitHub\",\n              children: /*#__PURE__*/_jsxDEV(FaGithub, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 66,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              className: \"social-link\",\n              \"aria-label\": \"LinkedIn\",\n              children: /*#__PURE__*/_jsxDEV(FaLinkedin, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 69,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              className: \"social-link\",\n              \"aria-label\": \"Twitter\",\n              children: /*#__PURE__*/_jsxDEV(FaTwitter, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 72,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              className: \"social-link\",\n              \"aria-label\": \"Instagram\",\n              children: /*#__PURE__*/_jsxDEV(FaInstagram, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 75,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-center lg:justify-end\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-80 h-80 lg:w-96 lg:h-96 rounded-full overflow-hidden border-4 border-white border-opacity-20 animate-float\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"https://via.placeholder.com/400x400/4f46e5/ffffff?text=MR\",\n                alt: \"Mahmoud Rady\",\n                className: \"w-full h-full object-cover\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute -top-4 -right-4 w-20 h-20 bg-yellow-400 rounded-full opacity-20 animate-pulse\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute -bottom-4 -left-4 w-16 h-16 bg-white rounded-full opacity-10 animate-pulse delay-1000\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 5\n  }, this);\n};\n_s(Hero, \"Z72AqRalvcuvuUyHd5OLwsN/cpk=\");\n_c = Hero;\nexport default Hero;\nvar _c;\n$RefreshReg$(_c, \"Hero\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FaLinkedin", "FaTwitter", "FaInstagram", "jsxDEV", "_jsxDEV", "Hero", "_s", "displayText", "setDisplayText", "fullText", "typingSpeed", "index", "timer", "setInterval", "length", "slice", "clearInterval", "scrollToSection", "sectionId", "element", "document", "getElementById", "scrollIntoView", "behavior", "id", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "href", "size", "src", "alt", "_c", "$RefreshReg$"], "sources": ["E:/test_refactor/portofolio/mahmoud-portfolio/src/components/Hero.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { FaGith<PERSON>, Fa<PERSON><PERSON>ed<PERSON>, Fa<PERSON><PERSON><PERSON>, FaInstagram } from 'react-icons/fa';\n\nconst Hero = () => {\n  const [displayText, setDisplayText] = useState('');\n  const fullText = 'B Developer';\n  const typingSpeed = 100;\n\n  useEffect(() => {\n    let index = 0;\n    const timer = setInterval(() => {\n      if (index < fullText.length) {\n        setDisplayText(fullText.slice(0, index + 1));\n        index++;\n      } else {\n        clearInterval(timer);\n      }\n    }, typingSpeed);\n\n    return () => clearInterval(timer);\n  }, []);\n\n  const scrollToSection = (sectionId) => {\n    const element = document.getElementById(sectionId);\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n    }\n  };\n\n  return (\n    <section id=\"home\" className=\"min-h-screen flex items-center bg-hero-pattern text-white pt-20\">\n      <div className=\"container-custom\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n          {/* Content */}\n          <div className=\"text-center lg:text-left\">\n            <h1 className=\"text-4xl md:text-5xl lg:text-6xl font-bold mb-4 leading-tight\">\n              Hi, I'm <span className=\"text-yellow-400\">Mahmoud Rady</span>\n            </h1>\n            <h2 className=\"text-xl md:text-2xl lg:text-3xl mb-6 opacity-90 min-h-[2rem]\">\n              {displayText}\n              <span className=\"animate-pulse\">|</span>\n            </h2>\n            <p className=\"text-lg md:text-xl mb-8 opacity-80 max-w-2xl mx-auto lg:mx-0\">\n              I create beautiful and functional web experiences with modern technologies.\n            </p>\n            \n            {/* Buttons */}\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center lg:justify-start mb-8\">\n              <button \n                onClick={() => scrollToSection('portfolio')}\n                className=\"btn btn-primary\"\n              >\n                View My Work\n              </button>\n              <button \n                onClick={() => scrollToSection('contact')}\n                className=\"btn btn-secondary\"\n              >\n                Get In Touch\n              </button>\n            </div>\n\n            {/* Social Links */}\n            <div className=\"flex gap-4 justify-center lg:justify-start\">\n              <a href=\"#\" className=\"social-link\" aria-label=\"GitHub\">\n                <FaGithub size={20} />\n              </a>\n              <a href=\"#\" className=\"social-link\" aria-label=\"LinkedIn\">\n                <FaLinkedin size={20} />\n              </a>\n              <a href=\"#\" className=\"social-link\" aria-label=\"Twitter\">\n                <FaTwitter size={20} />\n              </a>\n              <a href=\"#\" className=\"social-link\" aria-label=\"Instagram\">\n                <FaInstagram size={20} />\n              </a>\n            </div>\n          </div>\n\n          {/* Image */}\n          <div className=\"flex justify-center lg:justify-end\">\n            <div className=\"relative\">\n              <div className=\"w-80 h-80 lg:w-96 lg:h-96 rounded-full overflow-hidden border-4 border-white border-opacity-20 animate-float\">\n                <img \n                  src=\"https://via.placeholder.com/400x400/4f46e5/ffffff?text=MR\" \n                  alt=\"Mahmoud Rady\"\n                  className=\"w-full h-full object-cover\"\n                />\n              </div>\n              {/* Decorative elements */}\n              <div className=\"absolute -top-4 -right-4 w-20 h-20 bg-yellow-400 rounded-full opacity-20 animate-pulse\"></div>\n              <div className=\"absolute -bottom-4 -left-4 w-16 h-16 bg-white rounded-full opacity-10 animate-pulse delay-1000\"></div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Hero;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,EAAEC,WAAW,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9E,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAMY,QAAQ,GAAG,aAAa;EAC9B,MAAMC,WAAW,GAAG,GAAG;EAEvBZ,SAAS,CAAC,MAAM;IACd,IAAIa,KAAK,GAAG,CAAC;IACb,MAAMC,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC9B,IAAIF,KAAK,GAAGF,QAAQ,CAACK,MAAM,EAAE;QAC3BN,cAAc,CAACC,QAAQ,CAACM,KAAK,CAAC,CAAC,EAAEJ,KAAK,GAAG,CAAC,CAAC,CAAC;QAC5CA,KAAK,EAAE;MACT,CAAC,MAAM;QACLK,aAAa,CAACJ,KAAK,CAAC;MACtB;IACF,CAAC,EAAEF,WAAW,CAAC;IAEf,OAAO,MAAMM,aAAa,CAACJ,KAAK,CAAC;EACnC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,eAAe,GAAIC,SAAS,IAAK;IACrC,MAAMC,OAAO,GAAGC,QAAQ,CAACC,cAAc,CAACH,SAAS,CAAC;IAClD,IAAIC,OAAO,EAAE;MACXA,OAAO,CAACG,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IAChD;EACF,CAAC;EAED,oBACEnB,OAAA;IAASoB,EAAE,EAAC,MAAM;IAACC,SAAS,EAAC,iEAAiE;IAAAC,QAAA,eAC5FtB,OAAA;MAAKqB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/BtB,OAAA;QAAKqB,SAAS,EAAC,qDAAqD;QAAAC,QAAA,gBAElEtB,OAAA;UAAKqB,SAAS,EAAC,0BAA0B;UAAAC,QAAA,gBACvCtB,OAAA;YAAIqB,SAAS,EAAC,+DAA+D;YAAAC,QAAA,GAAC,UACpE,eAAAtB,OAAA;cAAMqB,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eACL1B,OAAA;YAAIqB,SAAS,EAAC,8DAA8D;YAAAC,QAAA,GACzEnB,WAAW,eACZH,OAAA;cAAMqB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eACL1B,OAAA;YAAGqB,SAAS,EAAC,8DAA8D;YAAAC,QAAA,EAAC;UAE5E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAGJ1B,OAAA;YAAKqB,SAAS,EAAC,sEAAsE;YAAAC,QAAA,gBACnFtB,OAAA;cACE2B,OAAO,EAAEA,CAAA,KAAMd,eAAe,CAAC,WAAW,CAAE;cAC5CQ,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAC5B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT1B,OAAA;cACE2B,OAAO,EAAEA,CAAA,KAAMd,eAAe,CAAC,SAAS,CAAE;cAC1CQ,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAC9B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGN1B,OAAA;YAAKqB,SAAS,EAAC,4CAA4C;YAAAC,QAAA,gBACzDtB,OAAA;cAAG4B,IAAI,EAAC,GAAG;cAACP,SAAS,EAAC,aAAa;cAAC,cAAW,QAAQ;cAAAC,QAAA,eACrDtB,OAAA,CAACL,QAAQ;gBAACkC,IAAI,EAAE;cAAG;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eACJ1B,OAAA;cAAG4B,IAAI,EAAC,GAAG;cAACP,SAAS,EAAC,aAAa;cAAC,cAAW,UAAU;cAAAC,QAAA,eACvDtB,OAAA,CAACJ,UAAU;gBAACiC,IAAI,EAAE;cAAG;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACJ1B,OAAA;cAAG4B,IAAI,EAAC,GAAG;cAACP,SAAS,EAAC,aAAa;cAAC,cAAW,SAAS;cAAAC,QAAA,eACtDtB,OAAA,CAACH,SAAS;gBAACgC,IAAI,EAAE;cAAG;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACJ1B,OAAA;cAAG4B,IAAI,EAAC,GAAG;cAACP,SAAS,EAAC,aAAa;cAAC,cAAW,WAAW;cAAAC,QAAA,eACxDtB,OAAA,CAACF,WAAW;gBAAC+B,IAAI,EAAE;cAAG;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN1B,OAAA;UAAKqB,SAAS,EAAC,oCAAoC;UAAAC,QAAA,eACjDtB,OAAA;YAAKqB,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBtB,OAAA;cAAKqB,SAAS,EAAC,8GAA8G;cAAAC,QAAA,eAC3HtB,OAAA;gBACE8B,GAAG,EAAC,2DAA2D;gBAC/DC,GAAG,EAAC,cAAc;gBAClBV,SAAS,EAAC;cAA4B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN1B,OAAA;cAAKqB,SAAS,EAAC;YAAwF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC9G1B,OAAA;cAAKqB,SAAS,EAAC;YAAgG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACxB,EAAA,CA/FID,IAAI;AAAA+B,EAAA,GAAJ/B,IAAI;AAiGV,eAAeA,IAAI;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}