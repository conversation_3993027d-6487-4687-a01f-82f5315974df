import React, { useState, useEffect, useRef } from "react";
import { FaDownload } from "react-icons/fa";

const About = () => {
  const [skillsAnimated, setSkillsAnimated] = useState(false);
  const skillsRef = useRef(null);

  const skills = [
    { name: "HTML/CSS", percentage: 95 },
    { name: "JavaScript", percentage: 90 },
    { name: "React", percentage: 85 },
    { name: "Node.js", percentage: 90 },
    { name: "MongoDB", percentage: 85 },
    { name: "Express.js", percentage: 90 },
  ];

  const personalInfo = [
    { label: "Name", value: "<PERSON>" },
    { label: "Email", value: "<EMAIL>" },
    { label: "Phone", value: "+01018551242" },
    { label: "Location", value: "Assiut, Egypt" },
    { label: "Experience", value: "2+ Years" },
    { label: "Freelance", value: "Available" },
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !skillsAnimated) {
            setSkillsAnimated(true);
          }
        });
      },
      { threshold: 0.3 }
    );

    if (skillsRef.current) {
      observer.observe(skillsRef.current);
    }

    return () => {
      if (skillsRef.current) {
        observer.unobserve(skillsRef.current);
      }
    };
  }, [skillsAnimated]);

  return (
    <section id="about" className="section-padding bg-gray-50">
      <div className="container-custom">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="section-title">About Me</h2>
          <p className="section-subtitle">Get to know more about me</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
          {/* About Text & Info */}
          <div className="animate-on-scroll">
            <h3 className="text-2xl md:text-3xl font-bold mb-6 text-gray-800">
              I'm a Backend Developer
            </h3>
            <p className="text-gray-600 mb-8 leading-relaxed">
              I'm a passionate backend developer with expertise in creating
              full-stack web applications. I specialize in building robust
              e-commerce platforms and have successfully developed
              WebDecor&More, a comprehensive home decor platform handling 100+
              product listings with optimized performance. I improved system
              response time by 40% using optimized backend queries in Node.js,
              demonstrating my commitment to efficient and scalable solutions.
            </p>

            {/* Personal Info Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
              {personalInfo.map((info, index) => (
                <div
                  key={index}
                  className="flex justify-between items-center py-2 border-b border-gray-200"
                >
                  <span className="font-semibold text-gray-700">
                    {info.label}:
                  </span>
                  <span className="text-gray-600">{info.value}</span>
                </div>
              ))}
            </div>

            {/* Download CV Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <a
                href="/Mohamed_Ayman_CV_2025.pdf"
                download="Mohamed_Ayman_CV_2025.pdf"
                className="btn btn-primary inline-flex items-center gap-2 justify-center"
              >
                <FaDownload />
                Download CV (PDF)
              </a>
              <button
                onClick={() => {
                  // Open CV in new window for viewing/printing
                  const cvContent = `
                    <!DOCTYPE html>
                    <html>
                    <head>
                      <title>Mohamed Ayman - CV</title>
                      <meta charset="UTF-8">
                      <meta name="viewport" content="width=device-width, initial-scale=1.0">
                      <script src="https://cdn.tailwindcss.com"></meta>
                      <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
                      <style>
                        body {
                          font-family: 'Poppins', sans-serif;
                          background-color: #f8fafc;
                        }
                        .cv-container {
                          max-width: 210mm;
                          margin: 20px auto;
                          background: white;
                          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                          border-radius: 8px;
                          overflow: hidden;
                        }
                        @media print {
                          @page {
                            margin: 0.5in;
                            size: A4;
                          }
                          body {
                            font-size: 12pt;
                            line-height: 1.4;
                            background: white !important;
                          }
                          .cv-container {
                            box-shadow: none !important;
                            margin: 0 !important;
                            border-radius: 0 !important;
                          }
                          .print-hidden {
                            display: none !important;
                          }
                          .page-break {
                            page-break-before: always;
                          }
                        }
                        .section-border {
                          border-bottom: 2px solid #3b82f6;
                          padding-bottom: 4px;
                          margin-bottom: 12px;
                        }
                        .skill-item {
                          background: #f1f5f9;
                          padding: 4px 8px;
                          border-radius: 4px;
                          font-size: 0.875rem;
                        }
                      </style>
                    </head>
                    <body>
                      <div class="cv-container">
                        <div class="print-hidden p-4 bg-gray-100 border-b">
                          <div class="flex justify-between items-center">
                            <h3 class="text-lg font-semibold text-gray-800">Mohamed Ayman - CV</h3>
                            <button onclick="window.print()" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                              🖨️ Print / Save as PDF
                            </button>
                          </div>
                        </div>

                        <div class="p-8">
                          <!-- Header -->
                          <div class="text-center mb-8 pb-6 border-b-2 border-blue-600">
                            <h1 class="text-4xl font-bold text-gray-800 mb-2">Mohamed Ayman</h1>
                            <h2 class="text-xl text-blue-600 font-semibold mb-4">Backend Developer</h2>
                            <div class="flex flex-wrap justify-center gap-6 text-sm text-gray-600">
                              <div class="flex items-center gap-2">
                                <span class="text-blue-600">📧</span>
                                <span><EMAIL></span>
                              </div>
                              <div class="flex items-center gap-2">
                                <span class="text-blue-600">📱</span>
                                <span>+01018551242</span>
                              </div>
                              <div class="flex items-center gap-2">
                                <span class="text-blue-600">📍</span>
                                <span>Assiut, Egypt</span>
                              </div>
                            </div>
                          </div>

                          <!-- Professional Summary -->
                          <section class="mb-8">
                            <h3 class="section-border text-xl font-bold text-gray-800">Professional Summary</h3>
                            <div class="bg-gray-50 p-4 rounded-lg">
                              <p class="text-gray-700 leading-relaxed">
                                Passionate Backend Developer with expertise in creating full-stack web applications.
                                Specialized in building robust e-commerce platforms and have successfully developed
                                <strong>WebDecor&More</strong>, a comprehensive home decor platform handling 100+ product listings with
                                optimized performance. Improved system response time by <strong>40%</strong> using optimized backend
                                queries in Node.js.
                              </p>
                            </div>
                          </section>

                          <!-- Technical Skills -->
                          <section class="mb-8">
                            <h3 class="section-border text-xl font-bold text-gray-800">Technical Skills</h3>
                            <div class="grid grid-cols-2 gap-6">
                              <div class="bg-blue-50 p-4 rounded-lg">
                                <h4 class="font-bold text-blue-800 mb-3 text-center">Backend Technologies</h4>
                                <div class="space-y-2">
                                  <div class="skill-item text-center">Node.js & Express.js</div>
                                  <div class="skill-item text-center">MongoDB & Database Design</div>
                                  <div class="skill-item text-center">RESTful APIs Development</div>
                                  <div class="skill-item text-center">Authentication & Authorization</div>
                                </div>
                              </div>
                              <div class="bg-green-50 p-4 rounded-lg">
                                <h4 class="font-bold text-green-800 mb-3 text-center">Frontend Technologies</h4>
                                <div class="space-y-2">
                                  <div class="skill-item text-center">HTML5, CSS3, JavaScript</div>
                                  <div class="skill-item text-center">React.js & Modern Frameworks</div>
                                  <div class="skill-item text-center">Responsive Web Design</div>
                                  <div class="skill-item text-center">Tailwind CSS</div>
                                </div>
                              </div>
                            </div>
                          </section>

                          <!-- Key Projects -->
                          <section class="mb-8">
                            <h3 class="section-border text-xl font-bold text-gray-800">Key Projects</h3>
                            <div class="bg-gradient-to-r from-purple-50 to-pink-50 p-6 rounded-lg border-l-4 border-purple-500">
                              <div class="flex justify-between items-start mb-3">
                                <h4 class="text-lg font-bold text-purple-800">WebDecor&More Project</h4>
                                <span class="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-semibold">2024</span>
                              </div>
                              <p class="text-purple-700 font-medium mb-3">Full-Stack E-commerce Platform</p>
                              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                  <h5 class="font-semibold text-gray-800 mb-2">🛠️ Technologies Used:</h5>
                                  <div class="flex flex-wrap gap-2">
                                    <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">Node.js</span>
                                    <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">MongoDB</span>
                                    <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">Express.js</span>
                                    <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">JavaScript</span>
                                    <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">HTML/CSS</span>
                                  </div>
                                </div>
                                <div>
                                  <h5 class="font-semibold text-gray-800 mb-2">🎯 Key Achievements:</h5>
                                  <ul class="text-sm text-gray-700 space-y-1">
                                    <li>✅ 100+ product listings</li>
                                    <li>✅ 40% performance improvement</li>
                                    <li>✅ Full authentication system</li>
                                    <li>✅ Shopping cart functionality</li>
                                  </ul>
                                </div>
                              </div>
                            </div>
                          </section>

                          <!-- Experience -->
                          <section class="mb-8">
                            <h3 class="section-border text-xl font-bold text-gray-800">Professional Experience</h3>
                            <div class="bg-gray-50 p-6 rounded-lg border-l-4 border-blue-500">
                              <div class="flex justify-between items-start mb-3">
                                <h4 class="text-lg font-bold text-blue-800">Backend Developer</h4>
                                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-semibold">2021 - Present</span>
                              </div>
                              <p class="text-blue-700 font-medium mb-4">Freelance</p>
                              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <ul class="text-sm text-gray-700 space-y-2">
                                  <li class="flex items-start gap-2">
                                    <span class="text-green-500 mt-1">▶</span>
                                    <span>Developed and maintained backend systems for web applications</span>
                                  </li>
                                  <li class="flex items-start gap-2">
                                    <span class="text-green-500 mt-1">▶</span>
                                    <span>Implemented secure authentication and authorization systems</span>
                                  </li>
                                </ul>
                                <ul class="text-sm text-gray-700 space-y-2">
                                  <li class="flex items-start gap-2">
                                    <span class="text-green-500 mt-1">▶</span>
                                    <span>Optimized database queries and improved application performance</span>
                                  </li>
                                  <li class="flex items-start gap-2">
                                    <span class="text-green-500 mt-1">▶</span>
                                    <span>Collaborated with frontend developers for seamless user experiences</span>
                                  </li>
                                </ul>
                              </div>
                            </div>
                          </section>

                          <!-- Languages & Education -->
                          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                            <section>
                              <h3 class="section-border text-xl font-bold text-gray-800">Languages</h3>
                              <div class="space-y-3">
                                <div class="bg-green-50 p-3 rounded-lg flex justify-between items-center">
                                  <span class="font-semibold text-green-800">Arabic</span>
                                  <span class="bg-green-200 text-green-800 px-3 py-1 rounded-full text-sm font-semibold">Native</span>
                                </div>
                                <div class="bg-blue-50 p-3 rounded-lg flex justify-between items-center">
                                  <span class="font-semibold text-blue-800">English</span>
                                  <span class="bg-blue-200 text-blue-800 px-3 py-1 rounded-full text-sm font-semibold">Professional</span>
                                </div>
                              </div>
                            </section>

                            <section>
                              <h3 class="section-border text-xl font-bold text-gray-800">Education</h3>
                              <div class="bg-yellow-50 p-4 rounded-lg border-l-4 border-yellow-500">
                                <h4 class="font-bold text-yellow-800 mb-2">Computer Science</h4>
                                <p class="text-yellow-700 text-sm">University • Assiut, Egypt</p>
                              </div>
                            </section>
                          </div>

                          <!-- Footer -->
                          <div class="text-center pt-6 border-t-2 border-gray-200">
                            <p class="text-gray-600 text-sm">
                              Thank you for considering my application. I look forward to discussing how I can contribute to your team.
                            </p>
                            <div class="mt-4 flex justify-center space-x-4 text-sm text-gray-500">
                              <span>📧 <EMAIL></span>
                              <span>📱 +01018551242</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </body>
                    </html>
                  `;
                  const cvWindow = window.open("", "_blank");
                  cvWindow.document.write(cvContent);
                  cvWindow.document.close();
                }}
                className="btn btn-secondary inline-flex items-center gap-2 justify-center"
              >
                👁️ View CV
              </button>
            </div>
          </div>

          {/* Skills */}
          <div className="animate-on-scroll" ref={skillsRef}>
            <h3 className="text-2xl md:text-3xl font-bold mb-8 text-gray-800">
              My Skills
            </h3>
            <div className="space-y-6">
              {skills.map((skill, index) => (
                <div key={index} className="skill-item">
                  <div className="flex justify-between items-center mb-2">
                    <span className="font-medium text-gray-700">
                      {skill.name}
                    </span>
                    <span className="text-sm text-gray-500">
                      {skill.percentage}%
                    </span>
                  </div>
                  <div className="skill-bar">
                    <div
                      className={`skill-progress transition-all duration-1000 ease-out ${
                        skillsAnimated ? "w-full" : "w-0"
                      }`}
                      style={{
                        width: skillsAnimated ? `${skill.percentage}%` : "0%",
                        transitionDelay: `${index * 200}ms`,
                      }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-3 gap-6 mt-12">
              <div className="text-center">
                <div className="text-3xl font-bold text-primary-600 mb-2">
                  50+
                </div>
                <div className="text-gray-600">Projects Completed</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-primary-600 mb-2">
                  30+
                </div>
                <div className="text-gray-600">Happy Clients</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-primary-600 mb-2">
                  3+
                </div>
                <div className="text-gray-600">Years Experience</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
