import React, { useState, useEffect, useRef } from 'react';
import { FaDownload } from 'react-icons/fa';

const About = () => {
  const [skillsAnimated, setSkillsAnimated] = useState(false);
  const skillsRef = useRef(null);

  const skills = [
    { name: 'HTML/CSS', percentage: 95 },
    { name: 'JavaScript', percentage: 90 },
    { name: 'React', percentage: 85 },
    { name: 'Node.js', percentage: 90 },
    { name: 'MongoDB', percentage: 85 },
    { name: 'Express.js', percentage: 90 },
  ];

  const personalInfo = [
    { label: 'Name', value: '<PERSON>' },
    { label: 'Email', value: '<EMAIL>' },
    { label: 'Phone', value: '+01018551242' },
    { label: 'Location', value: 'Assiut, Egypt' },
    { label: 'Experience', value: '2+ Years' },
    { label: 'Freelance', value: 'Available' },
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !skillsAnimated) {
            setSkillsAnimated(true);
          }
        });
      },
      { threshold: 0.3 }
    );

    if (skillsRef.current) {
      observer.observe(skillsRef.current);
    }

    return () => {
      if (skillsRef.current) {
        observer.unobserve(skillsRef.current);
      }
    };
  }, [skillsAnimated]);

  return (
    <section id="about" className="section-padding bg-gray-50">
      <div className="container-custom">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="section-title">About Me</h2>
          <p className="section-subtitle">Get to know more about me</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
          {/* About Text & Info */}
          <div className="animate-on-scroll">
            <h3 className="text-2xl md:text-3xl font-bold mb-6 text-gray-800">
              I'm a Backend Developer
            </h3>
            <p className="text-gray-600 mb-8 leading-relaxed">
              I'm a passionate backend developer with expertise in creating responsive and
              user-friendly web applications. I love turning ideas into reality through clean,
              efficient code and modern design principles. My goal is to create digital experiences
              that not only look great but also provide exceptional user experiences.
            </p>

            {/* Personal Info Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
              {personalInfo.map((info, index) => (
                <div key={index} className="flex justify-between items-center py-2 border-b border-gray-200">
                  <span className="font-semibold text-gray-700">{info.label}:</span>
                  <span className="text-gray-600">{info.value}</span>
                </div>
              ))}
            </div>

            {/* Download CV Button */}
            <button className="btn btn-primary inline-flex items-center gap-2">
              <FaDownload />
              Download CV
            </button>
          </div>

          {/* Skills */}
          <div className="animate-on-scroll" ref={skillsRef}>
            <h3 className="text-2xl md:text-3xl font-bold mb-8 text-gray-800">
              My Skills
            </h3>
            <div className="space-y-6">
              {skills.map((skill, index) => (
                <div key={index} className="skill-item">
                  <div className="flex justify-between items-center mb-2">
                    <span className="font-medium text-gray-700">{skill.name}</span>
                    <span className="text-sm text-gray-500">{skill.percentage}%</span>
                  </div>
                  <div className="skill-bar">
                    <div 
                      className={`skill-progress transition-all duration-1000 ease-out ${
                        skillsAnimated ? 'w-full' : 'w-0'
                      }`}
                      style={{ 
                        width: skillsAnimated ? `${skill.percentage}%` : '0%',
                        transitionDelay: `${index * 200}ms`
                      }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-3 gap-6 mt-12">
              <div className="text-center">
                <div className="text-3xl font-bold text-primary-600 mb-2">50+</div>
                <div className="text-gray-600">Projects Completed</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-primary-600 mb-2">30+</div>
                <div className="text-gray-600">Happy Clients</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-primary-600 mb-2">3+</div>
                <div className="text-gray-600">Years Experience</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
