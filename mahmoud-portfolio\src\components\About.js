import React, { useState, useEffect, useRef } from "react";
import { FaDownload } from "react-icons/fa";

const About = () => {
  const [skillsAnimated, setSkillsAnimated] = useState(false);
  const skillsRef = useRef(null);

  const skills = [
    { name: "HTML/CSS", percentage: 95 },
    { name: "JavaScript", percentage: 90 },
    { name: "React", percentage: 85 },
    { name: "Node.js", percentage: 90 },
    { name: "MongoDB", percentage: 85 },
    { name: "Express.js", percentage: 90 },
  ];

  const personalInfo = [
    { label: "Name", value: "<PERSON>" },
    { label: "Email", value: "<EMAIL>" },
    { label: "Phone", value: "+01018551242" },
    { label: "Location", value: "Assiut, Egypt" },
    { label: "Experience", value: "2+ Years" },
    { label: "Freelance", value: "Available" },
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !skillsAnimated) {
            setSkillsAnimated(true);
          }
        });
      },
      { threshold: 0.3 }
    );

    if (skillsRef.current) {
      observer.observe(skillsRef.current);
    }

    return () => {
      if (skillsRef.current) {
        observer.unobserve(skillsRef.current);
      }
    };
  }, [skillsAnimated]);

  return (
    <section id="about" className="section-padding bg-gray-50">
      <div className="container-custom">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="section-title">About Me</h2>
          <p className="section-subtitle">Get to know more about me</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
          {/* About Text & Info */}
          <div className="animate-on-scroll">
            <h3 className="text-2xl md:text-3xl font-bold mb-6 text-gray-800">
              I'm a Backend Developer
            </h3>
            <p className="text-gray-600 mb-8 leading-relaxed">
              I'm a passionate backend developer with expertise in creating
              full-stack web applications. I specialize in building robust
              e-commerce platforms and have successfully developed
              WebDecor&More, a comprehensive home decor platform handling 100+
              product listings with optimized performance. I improved system
              response time by 40% using optimized backend queries in Node.js,
              demonstrating my commitment to efficient and scalable solutions.
            </p>

            {/* Personal Info Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
              {personalInfo.map((info, index) => (
                <div
                  key={index}
                  className="flex justify-between items-center py-2 border-b border-gray-200"
                >
                  <span className="font-semibold text-gray-700">
                    {info.label}:
                  </span>
                  <span className="text-gray-600">{info.value}</span>
                </div>
              ))}
            </div>

            {/* Download CV Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <a
                href="/Mohamed_Ayman_CV_2025.pdf"
                download="Mohamed_Ayman_CV_2025.pdf"
                className="btn btn-primary inline-flex items-center gap-2 justify-center"
              >
                <FaDownload />
                Download CV (PDF)
              </a>
              <button
                onClick={() => {
                  // Open CV in new window for viewing/printing
                  const cvContent = `
                    <!DOCTYPE html>
                    <html>
                    <head>
                      <title>Mohamed Ayman - CV</title>
                      <script src="https://cdn.tailwindcss.com"></script>
                      <style>
                        @media print {
                          @page { margin: 0.5in; }
                          body { font-size: 12pt; line-height: 1.4; }
                          .print-hidden { display: none !important; }
                        }
                      </style>
                    </head>
                    <body class="bg-white">
                      <div class="max-w-4xl mx-auto p-8">
                        <div class="print-hidden mb-4">
                          <button onclick="window.print()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">Print / Save as PDF</button>
                        </div>
                        <div class="text-center mb-8">
                          <h1 class="text-4xl font-bold text-gray-800 mb-2">Mohamed Ayman</h1>
                          <h2 class="text-xl text-gray-600 mb-4">Backend Developer</h2>
                          <div class="text-sm text-gray-600 space-y-1">
                            <div>📧 <EMAIL></div>
                            <div>📱 +01018551242</div>
                            <div>📍 Assiut, Egypt</div>
                          </div>
                        </div>

                        <section class="mb-6">
                          <h3 class="text-lg font-bold text-gray-800 border-b-2 border-blue-600 pb-1 mb-3">Professional Summary</h3>
                          <p class="text-gray-700 leading-relaxed">
                            Passionate Backend Developer with expertise in creating full-stack web applications.
                            Specialized in building robust e-commerce platforms and have successfully developed
                            WebDecor&More, a comprehensive home decor platform handling 100+ product listings with
                            optimized performance. Improved system response time by 40% using optimized backend
                            queries in Node.js.
                          </p>
                        </section>

                        <section class="mb-6">
                          <h3 class="text-lg font-bold text-gray-800 border-b-2 border-blue-600 pb-1 mb-3">Technical Skills</h3>
                          <div class="grid grid-cols-2 gap-4">
                            <div>
                              <h4 class="font-semibold text-gray-700 mb-2">Backend Technologies</h4>
                              <ul class="text-sm text-gray-600 space-y-1">
                                <li>• Node.js & Express.js</li>
                                <li>• MongoDB & Database Design</li>
                                <li>• RESTful APIs Development</li>
                                <li>• Authentication & Authorization</li>
                              </ul>
                            </div>
                            <div>
                              <h4 class="font-semibold text-gray-700 mb-2">Frontend Technologies</h4>
                              <ul class="text-sm text-gray-600 space-y-1">
                                <li>• HTML5, CSS3, JavaScript</li>
                                <li>• React.js & Modern Frameworks</li>
                                <li>• Responsive Web Design</li>
                                <li>• Tailwind CSS</li>
                              </ul>
                            </div>
                          </div>
                        </section>

                        <section class="mb-6">
                          <h3 class="text-lg font-bold text-gray-800 border-b-2 border-blue-600 pb-1 mb-3">Key Projects</h3>
                          <div class="mb-4">
                            <h4 class="font-semibold text-gray-800">WebDecor&More Project</h4>
                            <p class="text-sm text-gray-600 mb-2">Full-Stack E-commerce Platform | 2024</p>
                            <ul class="text-sm text-gray-700 space-y-1 ml-4">
                              <li>• Developed full-stack web application for e-commerce platform focused on home decor</li>
                              <li>• Utilized Node.js, MongoDB, Express.js, HTML, CSS, and JavaScript</li>
                              <li>• Implemented user authentication, product catalog, and shopping cart functionality</li>
                              <li>• Handled product listings for 100+ items</li>
                              <li>• Improved system response time by 40% using optimized backend queries</li>
                            </ul>
                          </div>
                        </section>

                        <section class="mb-6">
                          <h3 class="text-lg font-bold text-gray-800 border-b-2 border-blue-600 pb-1 mb-3">Experience</h3>
                          <div class="mb-4">
                            <h4 class="font-semibold text-gray-800">Backend Developer</h4>
                            <p class="text-sm text-gray-600 mb-2">Freelance | 2021 - Present</p>
                            <ul class="text-sm text-gray-700 space-y-1 ml-4">
                              <li>• Developed and maintained backend systems for web applications</li>
                              <li>• Implemented secure authentication and authorization systems</li>
                              <li>• Optimized database queries and improved application performance</li>
                              <li>• Collaborated with frontend developers for seamless user experiences</li>
                            </ul>
                          </div>
                        </section>

                        <section class="mb-6">
                          <h3 class="text-lg font-bold text-gray-800 border-b-2 border-blue-600 pb-1 mb-3">Languages</h3>
                          <div class="grid grid-cols-2 gap-4">
                            <div><span class="font-semibold text-gray-700">Arabic:</span> <span class="text-gray-600">Native</span></div>
                            <div><span class="font-semibold text-gray-700">English:</span> <span class="text-gray-600">Professional</span></div>
                          </div>
                        </section>
                      </div>
                    </body>
                    </html>
                  `;
                  const cvWindow = window.open("", "_blank");
                  cvWindow.document.write(cvContent);
                  cvWindow.document.close();
                }}
                className="btn btn-secondary inline-flex items-center gap-2 justify-center"
              >
                👁️ View CV
              </button>
            </div>
          </div>

          {/* Skills */}
          <div className="animate-on-scroll" ref={skillsRef}>
            <h3 className="text-2xl md:text-3xl font-bold mb-8 text-gray-800">
              My Skills
            </h3>
            <div className="space-y-6">
              {skills.map((skill, index) => (
                <div key={index} className="skill-item">
                  <div className="flex justify-between items-center mb-2">
                    <span className="font-medium text-gray-700">
                      {skill.name}
                    </span>
                    <span className="text-sm text-gray-500">
                      {skill.percentage}%
                    </span>
                  </div>
                  <div className="skill-bar">
                    <div
                      className={`skill-progress transition-all duration-1000 ease-out ${
                        skillsAnimated ? "w-full" : "w-0"
                      }`}
                      style={{
                        width: skillsAnimated ? `${skill.percentage}%` : "0%",
                        transitionDelay: `${index * 200}ms`,
                      }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-3 gap-6 mt-12">
              <div className="text-center">
                <div className="text-3xl font-bold text-primary-600 mb-2">
                  50+
                </div>
                <div className="text-gray-600">Projects Completed</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-primary-600 mb-2">
                  30+
                </div>
                <div className="text-gray-600">Happy Clients</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-primary-600 mb-2">
                  3+
                </div>
                <div className="text-gray-600">Years Experience</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
