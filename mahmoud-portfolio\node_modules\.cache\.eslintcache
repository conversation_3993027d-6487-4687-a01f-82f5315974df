[{"E:\\test_refactor\\portofolio\\mahmoud-portfolio\\src\\index.js": "1", "E:\\test_refactor\\portofolio\\mahmoud-portfolio\\src\\App.js": "2", "E:\\test_refactor\\portofolio\\mahmoud-portfolio\\src\\reportWebVitals.js": "3", "E:\\test_refactor\\portofolio\\mahmoud-portfolio\\src\\components\\About.js": "4", "E:\\test_refactor\\portofolio\\mahmoud-portfolio\\src\\components\\Footer.js": "5", "E:\\test_refactor\\portofolio\\mahmoud-portfolio\\src\\components\\Navbar.js": "6", "E:\\test_refactor\\portofolio\\mahmoud-portfolio\\src\\components\\Portfolio.js": "7", "E:\\test_refactor\\portofolio\\mahmoud-portfolio\\src\\components\\Contact.js": "8", "E:\\test_refactor\\portofolio\\mahmoud-portfolio\\src\\components\\Hero.js": "9", "E:\\test_refactor\\portofolio\\mahmoud-portfolio\\src\\components\\Services.js": "10", "E:\\test_refactor\\portofolio\\mahmoud-portfolio\\src\\hooks\\useScrollAnimation.js": "11"}, {"size": 535, "mtime": 1753373278074, "results": "12", "hashOfConfig": "13"}, {"size": 663, "mtime": 1753373659755, "results": "14", "hashOfConfig": "13"}, {"size": 362, "mtime": 1753373278491, "results": "15", "hashOfConfig": "13"}, {"size": 5610, "mtime": 1753375522287, "results": "16", "hashOfConfig": "13"}, {"size": 5562, "mtime": 1753373640753, "results": "17", "hashOfConfig": "13"}, {"size": 3771, "mtime": 1753375330532, "results": "18", "hashOfConfig": "13"}, {"size": 6306, "mtime": 1753376487967, "results": "19", "hashOfConfig": "13"}, {"size": 7249, "mtime": 1753376584518, "results": "20", "hashOfConfig": "13"}, {"size": 3572, "mtime": 1753375016367, "results": "21", "hashOfConfig": "13"}, {"size": 4190, "mtime": 1753375558919, "results": "22", "hashOfConfig": "13"}, {"size": 794, "mtime": 1753373649117, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "b7cunv", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\test_refactor\\portofolio\\mahmoud-portfolio\\src\\index.js", [], [], "E:\\test_refactor\\portofolio\\mahmoud-portfolio\\src\\App.js", [], [], "E:\\test_refactor\\portofolio\\mahmoud-portfolio\\src\\reportWebVitals.js", [], [], "E:\\test_refactor\\portofolio\\mahmoud-portfolio\\src\\components\\About.js", ["57"], [], "E:\\test_refactor\\portofolio\\mahmoud-portfolio\\src\\components\\Footer.js", ["58", "59", "60", "61"], [], "E:\\test_refactor\\portofolio\\mahmoud-portfolio\\src\\components\\Navbar.js", [], [], "E:\\test_refactor\\portofolio\\mahmoud-portfolio\\src\\components\\Portfolio.js", [], [], "E:\\test_refactor\\portofolio\\mahmoud-portfolio\\src\\components\\Contact.js", [], [], "E:\\test_refactor\\portofolio\\mahmoud-portfolio\\src\\components\\Hero.js", ["62", "63"], [], "E:\\test_refactor\\portofolio\\mahmoud-portfolio\\src\\components\\Services.js", [], [], "E:\\test_refactor\\portofolio\\mahmoud-portfolio\\src\\hooks\\useScrollAnimation.js", [], [], {"ruleId": "64", "severity": 1, "message": "65", "line": 44, "column": 38, "nodeType": "66", "endLine": 44, "endColumn": 45}, {"ruleId": "67", "severity": 1, "message": "68", "line": 93, "column": 15, "nodeType": "69", "endLine": 97, "endColumn": 16}, {"ruleId": "67", "severity": 1, "message": "68", "line": 100, "column": 15, "nodeType": "69", "endLine": 104, "endColumn": 16}, {"ruleId": "67", "severity": 1, "message": "68", "line": 107, "column": 15, "nodeType": "69", "endLine": 111, "endColumn": 16}, {"ruleId": "67", "severity": 1, "message": "68", "line": 114, "column": 15, "nodeType": "69", "endLine": 118, "endColumn": 16}, {"ruleId": "70", "severity": 1, "message": "71", "line": 2, "column": 32, "nodeType": "66", "messageId": "72", "endLine": 2, "endColumn": 41}, {"ruleId": "70", "severity": 1, "message": "73", "line": 2, "column": 43, "nodeType": "66", "messageId": "72", "endLine": 2, "endColumn": 54}, "react-hooks/exhaustive-deps", "The ref value 'skillsRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'skillsRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "Identifier", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "no-unused-vars", "'FaTwitter' is defined but never used.", "unusedVar", "'FaInstagram' is defined but never used."]