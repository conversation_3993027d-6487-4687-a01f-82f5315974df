{"ast": null, "code": "var _excluded = [\"attr\", \"size\", \"title\"];\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  for (var key in source) {\n    if (Object.prototype.hasOwnProperty.call(source, key)) {\n      if (excluded.indexOf(key) >= 0) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nimport React from \"react\";\nimport { IconContext, DefaultContext } from \"./iconContext.mjs\";\nfunction Tree2Element(tree) {\n  return tree && tree.map((node, i) => /*#__PURE__*/React.createElement(node.tag, _objectSpread({\n    key: i\n  }, node.attr), Tree2Element(node.child)));\n}\nexport function GenIcon(data) {\n  return props => /*#__PURE__*/React.createElement(IconBase, _extends({\n    attr: _objectSpread({}, data.attr)\n  }, props), Tree2Element(data.child));\n}\nexport function IconBase(props) {\n  var elem = conf => {\n    var {\n        attr,\n        size,\n        title\n      } = props,\n      svgProps = _objectWithoutProperties(props, _excluded);\n    var computedSize = size || conf.size || \"1em\";\n    var className;\n    if (conf.className) className = conf.className;\n    if (props.className) className = (className ? className + \" \" : \"\") + props.className;\n    return /*#__PURE__*/React.createElement(\"svg\", _extends({\n      stroke: \"currentColor\",\n      fill: \"currentColor\",\n      strokeWidth: \"0\"\n    }, conf.attr, attr, svgProps, {\n      className: className,\n      style: _objectSpread(_objectSpread({\n        color: props.color || conf.color\n      }, conf.style), props.style),\n      height: computedSize,\n      width: computedSize,\n      xmlns: \"http://www.w3.org/2000/svg\"\n    }), title && /*#__PURE__*/React.createElement(\"title\", null, title), props.children);\n  };\n  return IconContext !== undefined ? /*#__PURE__*/React.createElement(IconContext.Consumer, null, conf => elem(conf)) : elem(DefaultContext);\n}", "map": {"version": 3, "names": ["_excluded", "_objectWithoutProperties", "source", "excluded", "target", "_objectWithoutPropertiesLoose", "key", "i", "Object", "getOwnPropertySymbols", "sourceSymbolKeys", "length", "indexOf", "prototype", "propertyIsEnumerable", "call", "hasOwnProperty", "_extends", "assign", "bind", "arguments", "apply", "ownKeys", "e", "r", "t", "keys", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "_objectSpread", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "obj", "value", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "configurable", "writable", "_toPrimitive", "Symbol", "toPrimitive", "TypeError", "String", "Number", "React", "IconContext", "DefaultContext", "Tree2Element", "tree", "map", "node", "createElement", "tag", "attr", "child", "GenIcon", "data", "props", "IconBase", "elem", "conf", "size", "title", "svgProps", "computedSize", "className", "stroke", "fill", "strokeWidth", "style", "color", "height", "width", "xmlns", "children", "undefined", "Consumer"], "sources": ["E:/test_refactor/portofolio/node_modules/react-icons/lib/iconBase.mjs"], "sourcesContent": ["var _excluded = [\"attr\", \"size\", \"title\"];\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } } return target; }\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport React from \"react\";\nimport { IconContext, DefaultContext } from \"./iconContext.mjs\";\nfunction Tree2Element(tree) {\n  return tree && tree.map((node, i) => /*#__PURE__*/React.createElement(node.tag, _objectSpread({\n    key: i\n  }, node.attr), Tree2Element(node.child)));\n}\nexport function GenIcon(data) {\n  return props => /*#__PURE__*/React.createElement(IconBase, _extends({\n    attr: _objectSpread({}, data.attr)\n  }, props), Tree2Element(data.child));\n}\nexport function IconBase(props) {\n  var elem = conf => {\n    var {\n        attr,\n        size,\n        title\n      } = props,\n      svgProps = _objectWithoutProperties(props, _excluded);\n    var computedSize = size || conf.size || \"1em\";\n    var className;\n    if (conf.className) className = conf.className;\n    if (props.className) className = (className ? className + \" \" : \"\") + props.className;\n    return /*#__PURE__*/React.createElement(\"svg\", _extends({\n      stroke: \"currentColor\",\n      fill: \"currentColor\",\n      strokeWidth: \"0\"\n    }, conf.attr, attr, svgProps, {\n      className: className,\n      style: _objectSpread(_objectSpread({\n        color: props.color || conf.color\n      }, conf.style), props.style),\n      height: computedSize,\n      width: computedSize,\n      xmlns: \"http://www.w3.org/2000/svg\"\n    }), title && /*#__PURE__*/React.createElement(\"title\", null, title), props.children);\n  };\n  return IconContext !== undefined ? /*#__PURE__*/React.createElement(IconContext.Consumer, null, conf => elem(conf)) : elem(DefaultContext);\n}"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC;AACzC,SAASC,wBAAwBA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAAE,IAAID,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,MAAM,GAAGC,6BAA6B,CAACH,MAAM,EAAEC,QAAQ,CAAC;EAAE,IAAIG,GAAG,EAAEC,CAAC;EAAE,IAAIC,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIC,gBAAgB,GAAGF,MAAM,CAACC,qBAAqB,CAACP,MAAM,CAAC;IAAE,KAAKK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,gBAAgB,CAACC,MAAM,EAAEJ,CAAC,EAAE,EAAE;MAAED,GAAG,GAAGI,gBAAgB,CAACH,CAAC,CAAC;MAAE,IAAIJ,QAAQ,CAACS,OAAO,CAACN,GAAG,CAAC,IAAI,CAAC,EAAE;MAAU,IAAI,CAACE,MAAM,CAACK,SAAS,CAACC,oBAAoB,CAACC,IAAI,CAACb,MAAM,EAAEI,GAAG,CAAC,EAAE;MAAUF,MAAM,CAACE,GAAG,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;IAAE;EAAE;EAAE,OAAOF,MAAM;AAAE;AAC3e,SAASC,6BAA6BA,CAACH,MAAM,EAAEC,QAAQ,EAAE;EAAE,IAAID,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,MAAM,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIE,GAAG,IAAIJ,MAAM,EAAE;IAAE,IAAIM,MAAM,CAACK,SAAS,CAACG,cAAc,CAACD,IAAI,CAACb,MAAM,EAAEI,GAAG,CAAC,EAAE;MAAE,IAAIH,QAAQ,CAACS,OAAO,CAACN,GAAG,CAAC,IAAI,CAAC,EAAE;MAAUF,MAAM,CAACE,GAAG,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;IAAE;EAAE;EAAE,OAAOF,MAAM;AAAE;AACtR,SAASa,QAAQA,CAAA,EAAG;EAAEA,QAAQ,GAAGT,MAAM,CAACU,MAAM,GAAGV,MAAM,CAACU,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUf,MAAM,EAAE;IAAE,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGa,SAAS,CAACT,MAAM,EAAEJ,CAAC,EAAE,EAAE;MAAE,IAAIL,MAAM,GAAGkB,SAAS,CAACb,CAAC,CAAC;MAAE,KAAK,IAAID,GAAG,IAAIJ,MAAM,EAAE;QAAE,IAAIM,MAAM,CAACK,SAAS,CAACG,cAAc,CAACD,IAAI,CAACb,MAAM,EAAEI,GAAG,CAAC,EAAE;UAAEF,MAAM,CAACE,GAAG,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;QAAE;MAAE;IAAE;IAAE,OAAOF,MAAM;EAAE,CAAC;EAAE,OAAOa,QAAQ,CAACI,KAAK,CAAC,IAAI,EAAED,SAAS,CAAC;AAAE;AAClV,SAASE,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAGjB,MAAM,CAACkB,IAAI,CAACH,CAAC,CAAC;EAAE,IAAIf,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIkB,CAAC,GAAGnB,MAAM,CAACC,qBAAqB,CAACc,CAAC,CAAC;IAAEC,CAAC,KAAKG,CAAC,GAAGA,CAAC,CAACC,MAAM,CAAC,UAAUJ,CAAC,EAAE;MAAE,OAAOhB,MAAM,CAACqB,wBAAwB,CAACN,CAAC,EAAEC,CAAC,CAAC,CAACM,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEL,CAAC,CAACM,IAAI,CAACV,KAAK,CAACI,CAAC,EAAEE,CAAC,CAAC;EAAE;EAAE,OAAOF,CAAC;AAAE;AAC9P,SAASO,aAAaA,CAACT,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,SAAS,CAACT,MAAM,EAAEa,CAAC,EAAE,EAAE;IAAE,IAAIC,CAAC,GAAG,IAAI,IAAIL,SAAS,CAACI,CAAC,CAAC,GAAGJ,SAAS,CAACI,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACd,MAAM,CAACiB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACQ,OAAO,CAAC,UAAUT,CAAC,EAAE;MAAEU,eAAe,CAACX,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGhB,MAAM,CAAC2B,yBAAyB,GAAG3B,MAAM,CAAC4B,gBAAgB,CAACb,CAAC,EAAEf,MAAM,CAAC2B,yBAAyB,CAACV,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACd,MAAM,CAACiB,CAAC,CAAC,CAAC,CAACQ,OAAO,CAAC,UAAUT,CAAC,EAAE;MAAEhB,MAAM,CAAC6B,cAAc,CAACd,CAAC,EAAEC,CAAC,EAAEhB,MAAM,CAACqB,wBAAwB,CAACJ,CAAC,EAAED,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACtb,SAASW,eAAeA,CAACI,GAAG,EAAEhC,GAAG,EAAEiC,KAAK,EAAE;EAAEjC,GAAG,GAAGkC,cAAc,CAAClC,GAAG,CAAC;EAAE,IAAIA,GAAG,IAAIgC,GAAG,EAAE;IAAE9B,MAAM,CAAC6B,cAAc,CAACC,GAAG,EAAEhC,GAAG,EAAE;MAAEiC,KAAK,EAAEA,KAAK;MAAET,UAAU,EAAE,IAAI;MAAEW,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAEJ,GAAG,CAAChC,GAAG,CAAC,GAAGiC,KAAK;EAAE;EAAE,OAAOD,GAAG;AAAE;AAC3O,SAASE,cAAcA,CAACf,CAAC,EAAE;EAAE,IAAIlB,CAAC,GAAGoC,YAAY,CAAClB,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI,OAAOlB,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC1G,SAASoC,YAAYA,CAAClB,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI,OAAOC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAACmB,MAAM,CAACC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAKtB,CAAC,EAAE;IAAE,IAAIhB,CAAC,GAAGgB,CAAC,CAACR,IAAI,CAACU,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI,OAAOjB,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIuC,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAKtB,CAAC,GAAGuB,MAAM,GAAGC,MAAM,EAAEvB,CAAC,CAAC;AAAE;AACvT,OAAOwB,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,EAAEC,cAAc,QAAQ,mBAAmB;AAC/D,SAASC,YAAYA,CAACC,IAAI,EAAE;EAC1B,OAAOA,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEhD,CAAC,KAAK,aAAa0C,KAAK,CAACO,aAAa,CAACD,IAAI,CAACE,GAAG,EAAEzB,aAAa,CAAC;IAC5F1B,GAAG,EAAEC;EACP,CAAC,EAAEgD,IAAI,CAACG,IAAI,CAAC,EAAEN,YAAY,CAACG,IAAI,CAACI,KAAK,CAAC,CAAC,CAAC;AAC3C;AACA,OAAO,SAASC,OAAOA,CAACC,IAAI,EAAE;EAC5B,OAAOC,KAAK,IAAI,aAAab,KAAK,CAACO,aAAa,CAACO,QAAQ,EAAE9C,QAAQ,CAAC;IAClEyC,IAAI,EAAE1B,aAAa,CAAC,CAAC,CAAC,EAAE6B,IAAI,CAACH,IAAI;EACnC,CAAC,EAAEI,KAAK,CAAC,EAAEV,YAAY,CAACS,IAAI,CAACF,KAAK,CAAC,CAAC;AACtC;AACA,OAAO,SAASI,QAAQA,CAACD,KAAK,EAAE;EAC9B,IAAIE,IAAI,GAAGC,IAAI,IAAI;IACjB,IAAI;QACAP,IAAI;QACJQ,IAAI;QACJC;MACF,CAAC,GAAGL,KAAK;MACTM,QAAQ,GAAGnE,wBAAwB,CAAC6D,KAAK,EAAE9D,SAAS,CAAC;IACvD,IAAIqE,YAAY,GAAGH,IAAI,IAAID,IAAI,CAACC,IAAI,IAAI,KAAK;IAC7C,IAAII,SAAS;IACb,IAAIL,IAAI,CAACK,SAAS,EAAEA,SAAS,GAAGL,IAAI,CAACK,SAAS;IAC9C,IAAIR,KAAK,CAACQ,SAAS,EAAEA,SAAS,GAAG,CAACA,SAAS,GAAGA,SAAS,GAAG,GAAG,GAAG,EAAE,IAAIR,KAAK,CAACQ,SAAS;IACrF,OAAO,aAAarB,KAAK,CAACO,aAAa,CAAC,KAAK,EAAEvC,QAAQ,CAAC;MACtDsD,MAAM,EAAE,cAAc;MACtBC,IAAI,EAAE,cAAc;MACpBC,WAAW,EAAE;IACf,CAAC,EAAER,IAAI,CAACP,IAAI,EAAEA,IAAI,EAAEU,QAAQ,EAAE;MAC5BE,SAAS,EAAEA,SAAS;MACpBI,KAAK,EAAE1C,aAAa,CAACA,aAAa,CAAC;QACjC2C,KAAK,EAAEb,KAAK,CAACa,KAAK,IAAIV,IAAI,CAACU;MAC7B,CAAC,EAAEV,IAAI,CAACS,KAAK,CAAC,EAAEZ,KAAK,CAACY,KAAK,CAAC;MAC5BE,MAAM,EAAEP,YAAY;MACpBQ,KAAK,EAAER,YAAY;MACnBS,KAAK,EAAE;IACT,CAAC,CAAC,EAAEX,KAAK,IAAI,aAAalB,KAAK,CAACO,aAAa,CAAC,OAAO,EAAE,IAAI,EAAEW,KAAK,CAAC,EAAEL,KAAK,CAACiB,QAAQ,CAAC;EACtF,CAAC;EACD,OAAO7B,WAAW,KAAK8B,SAAS,GAAG,aAAa/B,KAAK,CAACO,aAAa,CAACN,WAAW,CAAC+B,QAAQ,EAAE,IAAI,EAAEhB,IAAI,IAAID,IAAI,CAACC,IAAI,CAAC,CAAC,GAAGD,IAAI,CAACb,cAAc,CAAC;AAC5I", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}