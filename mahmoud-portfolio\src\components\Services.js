import React from 'react';
import { FaCode, FaMobile, FaPaintBrush, FaRocket, FaDatabase, FaSearch } from 'react-icons/fa';

const Services = () => {
  const services = [
    {
      icon: <FaCode size={40} />,
      title: 'Web Development',
      description: 'Creating responsive and modern websites using the latest technologies and best practices.',
    },
    {
      icon: <FaMobile size={40} />,
      title: 'Responsive Design',
      description: 'Ensuring your website looks great and functions perfectly on all devices and screen sizes.',
    },
    {
      icon: <FaPaintBrush size={40} />,
      title: 'UI/UX Design',
      description: 'Designing intuitive and beautiful user interfaces that provide excellent user experiences.',
    },
    {
      icon: <FaRocket size={40} />,
      title: 'Performance Optimization',
      description: 'Optimizing websites for speed and performance to ensure the best user experience.',
    },
    {
      icon: <FaDatabase size={40} />,
      title: 'Backend Integration',
      description: 'Seamlessly connecting frontend applications with backend services and APIs.',
    },
    {
      icon: <FaSearch size={40} />,
      title: 'SEO Optimization',
      description: 'Implementing SEO best practices to improve your website\'s search engine visibility.',
    },
  ];

  return (
    <section id="services" className="section-padding">
      <div className="container-custom">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="section-title">My Services</h2>
          <p className="section-subtitle">What I can do for you</p>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <div 
              key={index} 
              className="card p-8 text-center animate-on-scroll group"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              {/* Icon */}
              <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-r from-primary-600 to-secondary-600 rounded-full flex items-center justify-center text-white group-hover:scale-110 transition-transform duration-300">
                {service.icon}
              </div>

              {/* Title */}
              <h3 className="text-xl font-semibold mb-4 text-gray-800">
                {service.title}
              </h3>

              {/* Description */}
              <p className="text-gray-600 leading-relaxed">
                {service.description}
              </p>

              {/* Hover Effect */}
              <div className="mt-6 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div className="w-12 h-1 bg-gradient-to-r from-primary-600 to-secondary-600 mx-auto rounded-full"></div>
              </div>
            </div>
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center mt-16">
          <div className="bg-gradient-to-r from-primary-600 to-secondary-600 rounded-2xl p-8 md:p-12 text-white">
            <h3 className="text-2xl md:text-3xl font-bold mb-4">
              Ready to Start Your Project?
            </h3>
            <p className="text-lg mb-8 opacity-90 max-w-2xl mx-auto">
              Let's work together to bring your ideas to life. I'm here to help you create 
              amazing digital experiences that your users will love.
            </p>
            <button 
              onClick={() => {
                const element = document.getElementById('contact');
                if (element) {
                  element.scrollIntoView({ behavior: 'smooth' });
                }
              }}
              className="bg-white text-primary-600 px-8 py-3 rounded-full font-semibold hover:bg-gray-100 transition-colors duration-300"
            >
              Get Started
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Services;
